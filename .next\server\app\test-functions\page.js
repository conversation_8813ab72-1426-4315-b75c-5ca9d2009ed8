(()=>{var e={};e.id=1879,e.ids=[1879],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14769:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(60687),o=s(43210),i=s(92617),a=s(87979);function n(){let{user:e}=(0,a.hD)(),[t,s]=(0,o.useState)([]),[n,c]=(0,o.useState)(!1),d=(e,t,r,o)=>{s(s=>[...s,{test:e,success:t,data:r,error:o,timestamp:new Date().toISOString()}])},l=async()=>{c(!0),s([]);try{console.log("\uD83D\uDD0D Testing functions availability...");let e=await i.x8.areFunctionsAvailable();d("Functions Availability",e,{available:e})}catch(e){d("Functions Availability",!1,null,e)}if(e)try{console.log("\uD83D\uDD0D Testing dashboard data...");let t=await i.x8.getDashboardData(e.uid);d("Dashboard Data",!0,t)}catch(e){d("Dashboard Data",!1,null,e)}if(e&&["<EMAIL>","<EMAIL>"].includes(e.email||""))try{console.log("\uD83D\uDD0D Testing admin withdrawals...");let e=await i.x8.getAdminWithdrawals(!1);d("Admin Withdrawals",!0,e)}catch(e){d("Admin Withdrawals",!1,null,e)}c(!1)};return(0,r.jsx)("div",{className:"min-h-screen bg-gray-100 p-8",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"Firebase Functions Test"}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Connection Status"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"User:"})," ",e?e.email:"Not logged in"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Project ID:"})," ","mytube-india"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Auth Domain:"})," ","mytube-india.firebaseapp.com"]})]})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:(0,r.jsx)("button",{onClick:l,disabled:n,className:"bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600 disabled:opacity-50",children:n?"Testing...":"Test Firebase Functions"})}),t.length>0&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Test Results"}),(0,r.jsx)("div",{className:"space-y-4",children:t.map((e,t)=>(0,r.jsxs)("div",{className:`p-4 rounded border-l-4 ${e.success?"border-green-500 bg-green-50":"border-red-500 bg-red-50"}`,children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsx)("h3",{className:"font-semibold",children:e.test}),(0,r.jsx)("span",{className:`px-2 py-1 rounded text-sm ${e.success?"bg-green-200 text-green-800":"bg-red-200 text-red-800"}`,children:e.success?"SUCCESS":"FAILED"})]}),e.success&&e.data&&(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-1",children:"Response Data:"}),(0,r.jsx)("pre",{className:"bg-gray-100 p-2 rounded text-xs overflow-auto max-h-40",children:JSON.stringify(e.data,null,2)})]}),!e.success&&e.error&&(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-1",children:"Error Details:"}),(0,r.jsx)("pre",{className:"bg-gray-100 p-2 rounded text-xs overflow-auto max-h-40",children:JSON.stringify({name:e.error.name,message:e.error.message,code:e.error.code,details:e.error.details},null,2)})]}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:new Date(e.timestamp).toLocaleString()})]},t))})]})]})})}},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78435:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\test-functions\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\test-functions\\page.tsx","default")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},87701:(e,t,s)=>{Promise.resolve().then(s.bind(s,14769))},91645:e=>{"use strict";e.exports=require("net")},92617:(e,t,s)=>{"use strict";s.d(t,{x8:()=>b});var r=s(24791),o=s(33784);let i=(0,r.Qg)(o.Cn,"getUserDashboardData"),a=(0,r.Qg)(o.Cn,"submitVideoBatch"),n=(0,r.Qg)(o.Cn,"processWithdrawalRequest"),c=(0,r.Qg)(o.Cn,"getUserNotifications"),d=(0,r.Qg)(o.Cn,"getUserTransactions"),l=(0,r.Qg)(o.Cn,"getAdminWithdrawals"),u=(0,r.Qg)(o.Cn,"getAdminDashboardStats"),p=(0,r.Qg)(o.Cn,"getAdminUsers"),m=(0,r.Qg)(o.Cn,"getAdminNotifications"),f=(0,r.Qg)(o.Cn,"createAdminNotification"),h=new Map;async function g(e){try{console.log("\uD83D\uDE80 Using optimized dashboard data function for user:",e),console.log("\uD83D\uDD17 Functions instance:",o.Cn.app.options.projectId);let t=new Promise((e,t)=>{setTimeout(()=>t(Error("Function timeout")),5e3)}),s=await Promise.race([i({userId:e}),t]);if(console.log("\uD83D\uDCE1 Function response received:",s),s.data&&"object"==typeof s.data&&"success"in s.data){let e=s.data;if(e.success){console.log("✅ Dashboard data loaded via optimized function");let t=e.data;return{userData:{name:t.user.name,email:t.user.email,mobile:t.user.mobile,referralCode:t.user.referralCode,plan:t.user.plan,planExpiry:null,activeDays:t.user.activeDays},walletData:{wallet:t.user.wallet},videoData:{totalVideos:t.videos.total,todayVideos:t.videos.today,remainingVideos:t.videos.remaining}}}throw console.error("❌ Function returned success: false",e),Error("Function returned success: false")}throw console.error("❌ Invalid function response structure:",s),Error("Invalid response from dashboard function")}catch(e){throw console.error("❌ Error in optimized dashboard data:",e),console.error("❌ Error details:",{name:e?.name,message:e?.message,code:e?.code,details:e?.details}),e}}async function x(){try{console.log("\uD83D\uDE80 Using optimized admin dashboard stats function...");let e=await u({});if(e.data&&"object"==typeof e.data&&"success"in e.data){let t=e.data;if(t.success)return console.log("✅ Admin dashboard stats loaded via optimized function"),t.data}throw Error("Invalid response from admin dashboard stats function")}catch(e){throw console.error("❌ Error in optimized admin dashboard stats:",e),e}}let b={getDashboardData:async function(e){let t=`dashboard-${e}`,r=Date.now(),o=h.get(t);if(o&&r-o.timestamp<o.ttl)return console.log("\uD83C\uDFAF Using cached fallback dashboard data"),o.data;try{let s=await g(e);return h.set(t,{data:s,timestamp:r,ttl:6e4}),s}catch(m){console.warn("⚠️ Optimized function failed, falling back to direct calls:",m instanceof Error?m.message:String(m));let{getUserData:o,getWalletData:i,getVideoCountData:a}=await s.e(3582).then(s.bind(s,3582));console.log("\uD83D\uDCCA Loading dashboard data via direct Firestore calls...");let n=Date.now(),[c,d,l]=await Promise.all([o(e),i(e),a(e)]),u=Date.now()-n;console.log(`✅ Dashboard data loaded via fallback in ${u}ms`);let p={userData:c,walletData:d,videoData:l};return h.set(t,{data:p,timestamp:r,ttl:3e4}),p}},submitVideoBatch:async function(e,t=50){try{console.log("\uD83D\uDE80 Using optimized video batch submission...");let s=await a({userId:e,videoCount:t});if(s.data&&"object"==typeof s.data&&"success"in s.data){let e=s.data;if(e.success)return console.log("✅ Video batch submitted via optimized function"),e.data}throw Error("Invalid response from video batch function")}catch(e){throw console.error("❌ Error in optimized video batch submission:",e),e}},processWithdrawal:async function(e){try{console.log("\uD83D\uDE80 Using optimized withdrawal processing...");let t=await n(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Withdrawal processed via optimized function"),e.data}throw Error("Invalid response from withdrawal function")}catch(e){throw console.error("❌ Error in optimized withdrawal processing:",e),e}},getUserNotifications:async function(e,t=10){try{console.log("\uD83D\uDE80 Using optimized notifications function...");let s=await c({userId:e,limit:t});if(s.data&&"object"==typeof s.data&&"success"in s.data){let e=s.data;if(e.success)return console.log("✅ Notifications loaded via optimized function"),e.data}throw Error("Invalid response from notifications function")}catch(e){throw console.error("❌ Error in optimized notifications:",e),e}},getUserTransactions:async function(e,t=10,s="all"){try{console.log("\uD83D\uDE80 Using optimized transactions function...");let r=await d({userId:e,limit:t,type:s});if(r.data&&"object"==typeof r.data&&"success"in r.data){let e=r.data;if(e.success)return console.log("✅ Transactions loaded via optimized function"),e.data}throw Error("Invalid response from transactions function")}catch(e){throw console.error("❌ Error in optimized transactions:",e),e}},getAdminWithdrawals:async function(e=!1){try{console.log("\uD83D\uDE80 Using optimized admin withdrawals function, showAll:",e),console.log("\uD83D\uDD17 Functions instance:",o.Cn.app.options.projectId);let t=await l({showAllWithdrawals:e});if(console.log("\uD83D\uDCE1 Admin withdrawals function response received:",t),t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin withdrawals loaded via optimized function"),e.data;throw console.error("❌ Admin withdrawals function returned success: false",e),Error("Admin withdrawals function returned success: false")}throw console.error("❌ Invalid admin withdrawals function response structure:",t),Error("Invalid response from admin withdrawals function")}catch(e){throw console.error("❌ Error in optimized admin withdrawals:",e),console.error("❌ Error details:",{name:e?.name,message:e?.message,code:e?.code,details:e?.details}),e}},getAdminDashboardStats:async function(){try{return await x()}catch(t){console.warn("⚠️ Optimized admin stats function failed, falling back to direct calls");let{getAdminDashboardStats:e}=await Promise.all([s.e(3582),s.e(1391)]).then(s.bind(s,91391));return await e()}},getAdminUsers:async function(e={}){try{console.log("\uD83D\uDE80 Using optimized admin users function...");let t=await p(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin users loaded via optimized function"),e.data}throw Error("Invalid response from admin users function")}catch(e){throw console.error("❌ Error in optimized admin users:",e),e}},getAdminNotifications:async function(e=50,t="all"){try{console.log("\uD83D\uDE80 Using optimized admin notifications function...");let s=await m({limit:e,type:t});if(s.data&&"object"==typeof s.data&&"success"in s.data){let e=s.data;if(e.success)return console.log("✅ Admin notifications loaded via optimized function"),e.data}throw Error("Invalid response from admin notifications function")}catch(e){throw console.error("❌ Error in optimized admin notifications:",e),e}},createAdminNotification:async function(e){try{console.log("\uD83D\uDE80 Using optimized admin notification creation...");let t=await f(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin notification created via optimized function"),e.data}throw Error("Invalid response from admin notification creation function")}catch(e){throw console.error("❌ Error in optimized admin notification creation:",e),e}},areFunctionsAvailable:async function(){try{console.log("\uD83D\uDD0D Testing Firebase Functions connectivity..."),console.log("\uD83D\uDD17 Functions project:",o.Cn.app.options.projectId),console.log("\uD83D\uDD17 Functions region:",o.Cn.region);let e=await i({userId:"test"});return console.log("✅ Functions are available, test response:",e),!0}catch(e){return console.warn("⚠️ Firebase Functions not available, falling back to direct Firestore"),console.error("❌ Functions test error:",{name:e?.name,message:e?.message,code:e?.code,details:e?.details}),!1}}}},93155:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>p,tree:()=>d});var r=s(65239),o=s(48088),i=s(88170),a=s.n(i),n=s(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);s.d(t,c);let d={children:["",{children:["test-functions",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,78435)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\test-functions\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\test-functions\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/test-functions/page",pathname:"/test-functions",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},94735:e=>{"use strict";e.exports=require("events")},97429:(e,t,s)=>{Promise.resolve().then(s.bind(s,78435))}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[6204,6958,7567,8441,7979],()=>s(93155));module.exports=r})();