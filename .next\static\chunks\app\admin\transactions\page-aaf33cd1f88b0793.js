(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4426,6779,9160],{2846:(e,t,a)=>{"use strict";function r(e){if(console.log("\uD83D\uDD0D safeToDate input:",{value:e,type:typeof e,isDate:e instanceof Date,hasToDate:e&&"object"==typeof e&&"function"==typeof e.toDate,isNull:null===e,isUndefined:void 0===e}),e instanceof Date)return console.log("✅ Returning existing Date object:",e),e;if(e&&"object"==typeof e&&"function"==typeof e.toDate)try{let t=e.toDate();return console.log("✅ Converted Firestore Timestamp:",t),t}catch(e){return console.warn("❌ Failed to convert Firestore Timestamp:",e),new Date}if(e&&("string"==typeof e||"number"==typeof e)){let t=new Date(e);if(!isNaN(t.getTime()))return console.log("✅ Parsed string/number to date:",t),t}return console.error("❌ Invalid date value, cannot convert:",e),new Date("1970-01-01")}function n(e){return r(e).toLocaleDateString()}function o(e){return r(e).toLocaleTimeString()}function s(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Unknown",a=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Unknown";return e&&"string"==typeof e?e:t}(e,t);return a===t?a:a.charAt(0).toUpperCase()+a.slice(1)}a.d(t,{NI:()=>o,cI:()=>s,g1:()=>n,xi:()=>r})},3737:(e,t,a)=>{"use strict";function r(e,t,a){if(!e||0===e.length)return void alert("No data to export");let r=a||Object.keys(e[0]),n=["Account Number","Mobile Number","Mobile","Phone","Contact","User ID","Referral Code","IFSC Code","Bank Account","Account No"],o=new Blob(["\uFEFF"+[r.join(","),...e.map(e=>r.map(t=>{let a=e[t];if(null==a)return"";let r=n.some(e=>t.toLowerCase().includes(e.toLowerCase()));if("string"==typeof a){let e=a.replace(/"/g,'""');return'"'.concat(e,'"')}return a instanceof Date?'"'.concat(a.toLocaleDateString(),'"'):"object"==typeof a&&null!==a&&a.toDate?'"'.concat(a.toDate().toLocaleDateString(),'"'):r&&("number"==typeof a||!isNaN(Number(a)))?'"'.concat(a,'"'):"number"==typeof a?a.toString():'"'.concat(String(a),'"')}).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),s=document.createElement("a");if(void 0!==s.download){let e=URL.createObjectURL(o);s.setAttribute("href",e),s.setAttribute("download","".concat(t,"_").concat(new Date().toISOString().split("T")[0],".csv")),s.style.visibility="hidden",document.body.appendChild(s),s.click(),document.body.removeChild(s)}}function n(e){return e.map(e=>({"User ID":e.id||"",Name:e.name||"",Email:e.email||"",Mobile:String(e.mobile||""),"Referral Code":e.referralCode||"","Referred By":e.referredBy||"Direct",Plan:e.plan||"","Plan Expiry":e.planExpiry instanceof Date?e.planExpiry.toLocaleDateString():e.planExpiry?new Date(e.planExpiry).toLocaleDateString():"","Active Days":e.activeDays||0,"Total Videos":e.totalVideos||0,"Today Videos":e.todayVideos||0,"Last Video Date":e.lastVideoDate instanceof Date?e.lastVideoDate.toLocaleDateString():e.lastVideoDate?new Date(e.lastVideoDate).toLocaleDateString():"","Video Duration (seconds)":e.videoDuration||300,"Quick Video Advantage":e.quickVideoAdvantage?"Yes":"No","Quick Video Advantage Expiry":e.quickVideoAdvantageExpiry instanceof Date?e.quickVideoAdvantageExpiry.toLocaleDateString():e.quickVideoAdvantageExpiry?new Date(e.quickVideoAdvantageExpiry).toLocaleDateString():"","Quick Video Remaining Days":e.quickVideoAdvantageRemainingDays||0,"Quick Video Advantage Granted By":e.quickVideoAdvantageGrantedBy||"","Wallet Balance":e.wallet||0,"Referral Bonus Credited":e.referralBonusCredited?"Yes":"No",Status:e.status||"","Joined Date":e.joinedDate instanceof Date?e.joinedDate.toLocaleDateString():e.joinedDate?new Date(e.joinedDate).toLocaleDateString():"","Joined Time":e.joinedDate instanceof Date?e.joinedDate.toLocaleTimeString():e.joinedDate?new Date(e.joinedDate).toLocaleTimeString():""}))}function o(e){return e.map(e=>({"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","User Mobile":String(e.userMobile||""),Type:e.type||"",Amount:e.amount||0,Description:e.description||"",Status:e.status||"",Date:e.date instanceof Date?e.date.toLocaleDateString():e.date?new Date(e.date).toLocaleDateString():"",Time:e.date instanceof Date?e.date.toLocaleTimeString():e.date?new Date(e.date).toLocaleTimeString():""}))}function s(e){return e.map(e=>{var t,a,r,n;return{"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","Mobile Number":String(e.userMobile||""),"User Plan":e.userPlan||"","Active Days":e.userActiveDays||0,"Wallet Balance":e.walletBalance||0,"Withdrawal Amount":e.amount||0,"Account Holder Name":(null==(t=e.bankDetails)?void 0:t.accountHolderName)||"","Bank Name":(null==(a=e.bankDetails)?void 0:a.bankName)||"","Account Number":String((null==(r=e.bankDetails)?void 0:r.accountNumber)||""),"IFSC Code":(null==(n=e.bankDetails)?void 0:n.ifscCode)||"",Status:e.status||"pending","Request Date":e.requestDate instanceof Date?e.requestDate.toLocaleDateString():e.requestDate?new Date(e.requestDate).toLocaleDateString():"","Request Time":e.requestDate instanceof Date?e.requestDate.toLocaleTimeString():e.requestDate?new Date(e.requestDate).toLocaleTimeString():"","Admin Notes":e.adminNotes||""}})}function i(e){return e.map(e=>({Title:e.title,Message:e.message,Type:e.type,Target:e.target,Status:e.status,"Created Date":e.createdAt instanceof Date?e.createdAt.toLocaleDateString():e.createdAt?new Date(e.createdAt).toLocaleDateString():"","Sent Date":e.sentAt instanceof Date?e.sentAt.toLocaleDateString():e.sentAt?new Date(e.sentAt).toLocaleDateString():""}))}a.d(t,{Bf:()=>r,Fz:()=>n,Pe:()=>i,dB:()=>s,sL:()=>o})},6740:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>m});var r=a(5155),n=a(2115),o=a(6874),s=a.n(o),i=a(6681),l=a(6779),c=a(3737),d=a(2846),u=a(4752),g=a.n(u);function m(){let{user:e,loading:t,isAdmin:o}=(0,i.wC)(),[u,m]=(0,n.useState)([]),[p,h]=(0,n.useState)(!0),[x,w]=(0,n.useState)(""),[f,y]=(0,n.useState)(""),[D,N]=(0,n.useState)(""),[b,E]=(0,n.useState)(null),[v,L]=(0,n.useState)(!0);(0,n.useEffect)(()=>{o&&j(!0)},[o]);let j=async function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];try{h(!0);let t=await (0,l.I0)(20,e?null:b),r=[];for(let e of t.transactions){let t="Unknown User",n="<EMAIL>",o="N/A";try{let{getUserData:r}=await Promise.resolve().then(a.bind(a,3592)),s=await r(e.userId);s&&(t=s.name,n=s.email,o=s.mobile||"N/A")}catch(t){console.error("Error loading user data for transaction ".concat(e.id,":"),t)}r.push({id:e.id,userId:e.userId,userName:t,userEmail:n,userMobile:o,type:e.type,amount:e.amount,description:e.description,date:(0,d.xi)(e.date),status:e.status||"completed"})}e?m(r):m(e=>[...e,...r]),E(t.lastDoc),L(t.hasMore)}catch(e){console.error("Error loading transactions:",e),g().fire({icon:"error",title:"Error",text:"Failed to load transactions. Please try again."})}finally{h(!1)}},S=u.filter(e=>{let t=!x||e.type===x,a=!f||e.status===f,r=!D||String(e.userName||"").toLowerCase().includes(D.toLowerCase())||String(e.userEmail||"").toLowerCase().includes(D.toLowerCase())||String(e.userMobile||"").toLowerCase().includes(D.toLowerCase())||String(e.description||"").toLowerCase().includes(D.toLowerCase());return t&&a&&r}),C=e=>null==e||isNaN(e)?"₹0.00":new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0,maximumFractionDigits:2}).format(e),A=e=>{if(!e||"string"!=typeof e)return"Unknown";switch(e){case"video_earning":return"Video Earning";case"withdrawal":return"Withdrawal";case"bonus":return"Bonus";case"referral":return"Referral";default:return e.charAt(0).toUpperCase()+e.slice(1)}},I=e=>{switch(e){case"video_earning":return"fas fa-play-circle text-green-500";case"withdrawal":return"fas fa-download text-red-500";case"bonus":return"fas fa-gift text-yellow-500";case"referral":return"fas fa-users text-blue-500";default:return"fas fa-exchange-alt text-gray-500"}},M=async()=>{try{g().fire({title:"Exporting All Transactions",text:"Please wait while we prepare your export file with all transactions.",allowOutsideClick:!1,didOpen:()=>{g().showLoading()}}),console.log("\uD83D\uDD04 Loading all transactions for export...");let e=await (0,l.r2)();if(0===e.length)return void g().fire({icon:"warning",title:"No Data",text:"No transactions found in the database."});let t=[];for(let r of e){let e="Unknown User",n="<EMAIL>",o="N/A";try{let{getUserData:t}=await Promise.resolve().then(a.bind(a,3592)),s=await t(r.userId);s&&(e=s.name,n=s.email,o=s.mobile||"N/A")}catch(e){console.error("Error loading user data for transaction ".concat(r.id,":"),e)}t.push({id:r.id,userId:r.userId,userName:e,userEmail:n,userMobile:o,type:r.type,amount:r.amount,description:r.description,date:r.date,status:r.status||"completed"})}let r=(0,c.sL)(t);(0,c.Bf)(r,"all-transactions"),g().fire({icon:"success",title:"Export Complete",text:"Exported ".concat(t.length," total transactions to CSV file."),timer:3e3,showConfirmButton:!1})}catch(e){console.error("Error exporting all transactions:",e),g().fire({icon:"error",title:"Export Failed",text:"Failed to export all transactions. Please try again."})}};return t||p?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading transactions..."})]})}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(s(),{href:"/admin",className:"text-gray-500 hover:text-gray-700",children:(0,r.jsx)("i",{className:"fas fa-arrow-left text-xl"})}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Transactions"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-gray-700",children:["Total: ",S.length]}),(0,r.jsxs)("button",{onClick:()=>{if(0===S.length)return void g().fire({icon:"warning",title:"No Data",text:"No transactions to export."});let e=(0,c.sL)(S);(0,c.Bf)(e,"transactions"),g().fire({icon:"success",title:"Export Complete",text:"Exported ".concat(S.length," transactions to CSV file."),timer:2e3,showConfirmButton:!1})},className:"bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg",children:[(0,r.jsx)("i",{className:"fas fa-download mr-2"}),"Export Filtered"]}),(0,r.jsxs)("button",{onClick:M,className:"bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg",children:[(0,r.jsx)("i",{className:"fas fa-database mr-2"}),"Export All"]}),(0,r.jsxs)("button",{onClick:()=>j(!0),className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg",children:[(0,r.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})]})}),(0,r.jsx)("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Search"}),(0,r.jsx)("input",{type:"text",value:D,onChange:e=>N(e.target.value),placeholder:"Search user, email, mobile, or description...",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Type"}),(0,r.jsxs)("select",{value:x,onChange:e=>w(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"",children:"All Types"}),(0,r.jsx)("option",{value:"video_earning",children:"Video Earning"}),(0,r.jsx)("option",{value:"withdrawal",children:"Withdrawal"}),(0,r.jsx)("option",{value:"bonus",children:"Bonus"}),(0,r.jsx)("option",{value:"referral",children:"Referral"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,r.jsxs)("select",{value:f,onChange:e=>y(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"",children:"All Status"}),(0,r.jsx)("option",{value:"completed",children:"Completed"}),(0,r.jsx)("option",{value:"pending",children:"Pending"}),(0,r.jsx)("option",{value:"failed",children:"Failed"}),(0,r.jsx)("option",{value:"cancelled",children:"Cancelled"})]})]}),(0,r.jsx)("div",{className:"flex items-end",children:(0,r.jsx)("button",{onClick:()=>{w(""),y(""),N("")},className:"w-full bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg",children:"Clear Filters"})})]})}),(0,r.jsx)("div",{className:"p-6",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Mobile"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Description"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:S.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.userName}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.userEmail})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("div",{className:"text-sm text-gray-900",children:e.userMobile})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"".concat(I(e.type)," mr-2")}),(0,r.jsx)("span",{className:"text-sm text-gray-900",children:A(e.type)})]})}),(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsx)("div",{className:"text-sm text-gray-900",children:e.description})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("span",{className:"text-sm font-medium ".concat(e.amount>0?"text-green-600":"text-red-600"),children:[e.amount>0?"+":"",C(e.amount)]})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:[(0,d.g1)(e.date)," ",(0,d.NI)(e.date)]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat("completed"===e.status?"bg-green-100 text-green-800":"pending"===e.status?"bg-yellow-100 text-yellow-800":"failed"===e.status?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"),children:e.status?e.status.charAt(0).toUpperCase()+e.status.slice(1):"Unknown"})})]},e.id))})]})}),v&&(0,r.jsx)("div",{className:"bg-white px-4 py-3 border-t border-gray-200 text-center",children:(0,r.jsx)("button",{onClick:()=>{v&&!p&&j(!1)},disabled:p,className:"bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg disabled:opacity-50",children:p?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner w-4 h-4 inline-block mr-2"}),"Loading..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-plus mr-2"}),"Load More Transactions"]})})}),(0,r.jsxs)("div",{className:"bg-white px-4 py-3 border-t border-gray-200 text-center text-sm text-gray-600",children:["Showing ",u.length," transactions",!v&&u.length>0&&" (All loaded)"]})]})})]})}},6779:(e,t,a)=>{"use strict";a.d(t,{CF:()=>d,I0:()=>g,TK:()=>x,getAdminDashboardStats:()=>i,getAllPendingWithdrawals:()=>m,getAllWithdrawals:()=>p,hG:()=>w,lo:()=>l,nQ:()=>u,r2:()=>h,updateWithdrawalStatus:()=>f,x5:()=>c});var r=a(5317),n=a(6104),o=a(3592);let s=new Map;async function i(){let e="dashboard-stats",t=function(e){let t=s.get(e);return t&&Date.now()-t.timestamp<3e5?t.data:null}(e);if(t)return t;try{let t=new Date;t.setHours(0,0,0,0);let a=r.Dc.fromDate(t),i=await (0,r.getDocs)((0,r.collection)(n.db,o.COLLECTIONS.users)),l=i.size,c=(0,r.P)((0,r.collection)(n.db,o.COLLECTIONS.users),(0,r._M)(o.FIELD_NAMES.joinedDate,">=",a)),d=(await (0,r.getDocs)(c)).size,u=0,g=0,m=0,p=0;i.forEach(e=>{var a;let r=e.data();u+=r[o.FIELD_NAMES.totalVideos]||0,g+=r[o.FIELD_NAMES.wallet]||0;let n=null==(a=r[o.FIELD_NAMES.lastVideoDate])?void 0:a.toDate();n&&n.toDateString()===t.toDateString()&&(m+=r[o.FIELD_NAMES.todayVideos]||0)});try{let e=(0,r.P)((0,r.collection)(n.db,o.COLLECTIONS.transactions),(0,r._M)(o.FIELD_NAMES.type,"==","video_earning"),(0,r.AB)(1e3));(await (0,r.getDocs)(e)).forEach(e=>{var a;let r=e.data(),n=null==(a=r[o.FIELD_NAMES.date])?void 0:a.toDate();n&&n>=t&&(p+=r[o.FIELD_NAMES.amount]||0)})}catch(e){console.warn("Could not fetch today's transactions:",e)}let h=(0,r.P)((0,r.collection)(n.db,o.COLLECTIONS.withdrawals),(0,r._M)("status","==","pending")),x=(await (0,r.getDocs)(h)).size,w=(0,r.P)((0,r.collection)(n.db,o.COLLECTIONS.withdrawals),(0,r._M)("date",">=",a)),f=(await (0,r.getDocs)(w)).size,y={totalUsers:l,totalVideos:u,totalEarnings:g,pendingWithdrawals:x,todayUsers:d,todayVideos:m,todayEarnings:p,todayWithdrawals:f};return s.set(e,{data:y,timestamp:Date.now()}),y}catch(e){throw console.error("Error getting admin dashboard stats:",e),e}}async function l(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,r.P)((0,r.collection)(n.db,o.COLLECTIONS.users),(0,r.My)(o.FIELD_NAMES.joinedDate,"desc"),(0,r.AB)(e));t&&(a=(0,r.P)((0,r.collection)(n.db,o.COLLECTIONS.users),(0,r.My)(o.FIELD_NAMES.joinedDate,"desc"),(0,r.HM)(t),(0,r.AB)(e)));let s=await (0,r.getDocs)(a);return{users:s.docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[o.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[o.FIELD_NAMES.planExpiry])?void 0:a.toDate()}}),lastDoc:s.docs[s.docs.length-1]||null,hasMore:s.docs.length===e}}catch(e){throw console.error("Error getting users:",e),e}}async function c(e){try{if(!e||0===e.trim().length)return[];let t=e.toLowerCase().trim(),a=(0,r.P)((0,r.collection)(n.db,o.COLLECTIONS.users),(0,r.My)(o.FIELD_NAMES.joinedDate,"desc"));return(await (0,r.getDocs)(a)).docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[o.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[o.FIELD_NAMES.planExpiry])?void 0:a.toDate()}}).filter(e=>{let a=String(e[o.FIELD_NAMES.name]||"").toLowerCase(),r=String(e[o.FIELD_NAMES.email]||"").toLowerCase(),n=String(e[o.FIELD_NAMES.mobile]||"").toLowerCase(),s=String(e[o.FIELD_NAMES.referralCode]||"").toLowerCase();return a.includes(t)||r.includes(t)||n.includes(t)||s.includes(t)})}catch(e){throw console.error("Error searching users:",e),e}}async function d(){try{let e=(0,r.P)((0,r.collection)(n.db,o.COLLECTIONS.users),(0,r.My)(o.FIELD_NAMES.joinedDate,"desc"));return(await (0,r.getDocs)(e)).docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[o.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[o.FIELD_NAMES.planExpiry])?void 0:a.toDate()}})}catch(e){throw console.error("Error getting all users:",e),e}}async function u(){try{let e=(0,r.P)((0,r.collection)(n.db,o.COLLECTIONS.users));return(await (0,r.getDocs)(e)).size}catch(e){throw console.error("Error getting total user count:",e),e}}async function g(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,r.P)((0,r.collection)(n.db,o.COLLECTIONS.transactions),(0,r.My)(o.FIELD_NAMES.date,"desc"),(0,r.AB)(e));t&&(a=(0,r.P)((0,r.collection)(n.db,o.COLLECTIONS.transactions),(0,r.My)(o.FIELD_NAMES.date,"desc"),(0,r.HM)(t),(0,r.AB)(e)));let s=await (0,r.getDocs)(a);return{transactions:s.docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data()[o.FIELD_NAMES.date])?void 0:t.toDate()}}),lastDoc:s.docs[s.docs.length-1]||null,hasMore:s.docs.length===e}}catch(e){throw console.error("Error getting transactions:",e),e}}async function m(){try{console.log("\uD83D\uDD0D Loading ALL pending withdrawals...");let e=(0,r.P)((0,r.collection)(n.db,o.COLLECTIONS.withdrawals),(0,r._M)("status","==","pending"),(0,r.My)("date","desc")),t=(await (0,r.getDocs)(e)).docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data().date)?void 0:t.toDate()}});return console.log("✅ Loaded ".concat(t.length," pending withdrawals")),t}catch(e){throw console.error("Error getting all pending withdrawals:",e),e}}async function p(){try{console.log("\uD83D\uDD0D Loading ALL withdrawals...");let e=(0,r.P)((0,r.collection)(n.db,o.COLLECTIONS.withdrawals),(0,r.My)("date","desc")),t=(await (0,r.getDocs)(e)).docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data().date)?void 0:t.toDate()}});return console.log("✅ Loaded ".concat(t.length," total withdrawals")),t}catch(e){throw console.error("Error getting all withdrawals:",e),e}}async function h(){try{console.log("\uD83D\uDD0D Loading ALL transactions...");let e=(0,r.P)((0,r.collection)(n.db,o.COLLECTIONS.transactions),(0,r.My)(o.FIELD_NAMES.date,"desc")),t=(await (0,r.getDocs)(e)).docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data()[o.FIELD_NAMES.date])?void 0:t.toDate()}});return console.log("✅ Loaded ".concat(t.length," total transactions")),t}catch(e){throw console.error("Error getting all transactions:",e),e}}async function x(e,t){try{await (0,r.mZ)((0,r.H9)(n.db,o.COLLECTIONS.users,e),t),s.delete("dashboard-stats")}catch(e){throw console.error("Error updating user:",e),e}}async function w(e){try{await (0,r.kd)((0,r.H9)(n.db,o.COLLECTIONS.users,e)),s.delete("dashboard-stats")}catch(e){throw console.error("Error deleting user:",e),e}}async function f(e,t,i){try{let l=await (0,r.x7)((0,r.H9)(n.db,o.COLLECTIONS.withdrawals,e));if(!l.exists())throw Error("Withdrawal not found");let{userId:c,amount:d,status:u}=l.data(),g={status:t,updatedAt:r.Dc.now()};if(i&&(g.adminNotes=i),await (0,r.mZ)((0,r.H9)(n.db,o.COLLECTIONS.withdrawals,e),g),"approved"===t&&"approved"!==u){let{addTransaction:e}=await Promise.resolve().then(a.bind(a,3592));await e(c,{type:"withdrawal_approved",amount:0,description:"Withdrawal approved - ₹".concat(d," processed for transfer")})}if("rejected"===t&&"rejected"!==u){let{updateWalletBalance:e,addTransaction:t}=await Promise.resolve().then(a.bind(a,3592));await e(c,d),await t(c,{type:"withdrawal_rejected",amount:d,description:"Withdrawal rejected - ₹".concat(d," credited back to wallet")})}s.delete("dashboard-stats")}catch(e){throw console.error("Error updating withdrawal status:",e),e}}},7699:(e,t,a)=>{Promise.resolve().then(a.bind(a,6740))}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,8818,6874,3592,6681,8441,1684,7358],()=>t(7699)),_N_E=e.O()}]);