(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6834],{5212:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>o});var a=n(5155),r=n(2115),i=n(6104),s=n(3004),c=n(5317);function o(){let[e,t]=(0,r.useState)(""),[o,l]=(0,r.useState)(!1),d=async()=>{t(""),l(!0);try{t("\uD83D\uDD0D Testing Complete Registration Flow...\n"),t(e=>e+"Environment: ".concat(window.location.origin,"\n")),t(e=>e+"Project ID: ".concat("mytube-india","\n")),t(e=>e+"Auth Domain: ".concat("mytube-india.firebaseapp.com","\n\n")),t(e=>e+"\uD83D\uDCE1 Test 1: Firebase Auth User Creation\n");let e="regtest".concat(Date.now(),"@example.com");try{t(t=>t+"Creating user: ".concat(e,"\n"));let a=(await (0,s.eJ)(i.j2,e,"regtest123456")).user;t(e=>e+"✅ Auth user created: ".concat(a.uid,"\n")),t(e=>e+"   Email: ".concat(a.email,"\n")),t(e=>e+"   Email verified: ".concat(a.emailVerified,"\n")),await new Promise(e=>setTimeout(e,1e3)),t(e=>e+"   Auth state: ".concat(i.j2.currentUser?"authenticated":"not authenticated","\n")),t(e=>e+"\n\uD83D\uDCE1 Test 2: Creating User Document (Registration Style)\n");let{FIELD_NAMES:r,COLLECTIONS:o}=await n.e(3592).then(n.bind(n,3592)),l=Date.now().toString().slice(-4),d=Math.random().toString(36).substring(2,4).toUpperCase(),u="MY".concat(l).concat(d);t(e=>e+"Generated referral code: ".concat(u,"\n"));let m={[r.name]:"Registration Test User",[r.email]:e.toLowerCase(),[r.mobile]:"9876543210",[r.referralCode]:u,[r.referredBy]:"",[r.referralBonusCredited]:!1,[r.plan]:"Trial",[r.planExpiry]:null,[r.activeDays]:0,[r.joinedDate]:new Date,[r.wallet]:0,[r.totalVideos]:0,[r.todayVideos]:0,[r.lastVideoDate]:null,[r.videoDuration]:30,status:"active"};t(e=>e+"Document path: ".concat(o.users,"/").concat(a.uid,"\n")),t(e=>e+"Field count: ".concat(Object.keys(m).length,"\n")),t(e=>{var t;return e+"Current auth UID: ".concat(null==(t=i.j2.currentUser)?void 0:t.uid,"\n")}),t(e=>e+"Target UID: ".concat(a.uid,"\n")),t(e=>{var t;return e+"UIDs match: ".concat((null==(t=i.j2.currentUser)?void 0:t.uid)===a.uid,"\n")});let h=(0,c.H9)(i.db,o.users,a.uid);try{t(e=>e+"\nAttempting setDoc...\n"),await (0,c.BN)(h,m),t(e=>e+"✅ User document created successfully!\n");let e=await (0,c.x7)(h);if(e.exists()){let n=e.data();t(e=>e+"✅ Document verification successful\n"),t(e=>e+"   Name: ".concat(n[r.name],"\n")),t(e=>e+"   Email: ".concat(n[r.email],"\n")),t(e=>e+"   Plan: ".concat(n[r.plan],"\n")),t(e=>e+"   Referral Code: ".concat(n[r.referralCode],"\n")),t(e=>e+"\n\uD83C\uDF89 REGISTRATION TEST SUCCESSFUL!\n"),t(e=>e+"The registration flow works perfectly.\n"),t(e=>e+"If registration is failing, check for:\n"),t(e=>e+"- Form validation errors\n"),t(e=>e+"- Network connectivity issues\n"),t(e=>e+"- Browser console errors\n")}else t(e=>e+"❌ Document verification failed\n")}catch(e){t(t=>t+"❌ setDoc failed: ".concat(e.message,"\n")),t(t=>t+"   Error code: ".concat(e.code,"\n")),t(t=>t+"   Full error: ".concat(JSON.stringify(e,null,2),"\n")),"permission-denied"===e.code&&(t(e=>e+"\n\uD83D\uDD27 PERMISSION ISSUE DETECTED:\n"),t(e=>e+"   - Check Firestore security rules\n"),t(e=>e+"   - Ensure rules allow authenticated users to write their own documents\n"),t(e=>e+"   - Verify the user is properly authenticated\n"))}try{await a.delete(),t(e=>e+"✅ Test user deleted\n")}catch(e){t(t=>t+"⚠️ User deletion failed: ".concat(e.message,"\n"))}}catch(e){t(t=>t+"❌ Auth user creation failed: ".concat(e.message,"\n")),t(t=>t+"   Code: ".concat(e.code,"\n")),"auth/network-request-failed"===e.code&&(t(e=>e+"\n\uD83D\uDD27 NETWORK ISSUE DETECTED:\n"),t(e=>e+"   - Check your internet connection\n"),t(e=>e+"   - Try disabling VPN/proxy\n"),t(e=>e+"   - Check if firewall is blocking Firebase\n"),t(e=>e+"   - Try testing on a different network\n"))}}catch(e){t(t=>t+"❌ Test failed: ".concat(e.message,"\n")),t(t=>t+"   Code: ".concat(e.code,"\n"))}finally{l(!1)}};return(0,a.jsx)("div",{className:"min-h-screen p-4",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Registration Flow Test"}),(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsx)("button",{onClick:d,disabled:o,className:"btn-primary mb-4",children:o?"Testing Registration...":"Test Complete Registration Flow"}),(0,a.jsx)("div",{className:"bg-black/50 p-4 rounded-lg",children:(0,a.jsx)("pre",{className:"text-white text-sm whitespace-pre-wrap",children:e||'Click "Test Complete Registration Flow" to start...'})})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("a",{href:"/register",className:"text-blue-400 hover:text-blue-300 underline",children:"← Back to Registration"})})]})})}},6104:(e,t,n)=>{"use strict";n.d(t,{Cn:()=>u,db:()=>d,j2:()=>l});var a=n(3915),r=n(3004),i=n(5317),s=n(858),c=n(2144);let o=(0,a.Dk)().length?(0,a.Sx)():(0,a.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),l=(0,r.xI)(o),d=(0,i.aU)(o);(0,s.c7)(o);let u=(0,c.Uz)(o,"us-central1")},7642:(e,t,n)=>{Promise.resolve().then(n.bind(n,5212))}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8818,8441,1684,7358],()=>t(7642)),_N_E=e.O()}]);