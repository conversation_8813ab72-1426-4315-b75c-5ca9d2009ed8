(()=>{var e={};e.id=6174,e.ids=[6174],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},744:(e,s,t)=>{"use strict";t.d(s,{J:()=>i});var r=t(43210),a=t(3582);function i(e){let[s,t]=(0,r.useState)(!1),[i,n]=(0,r.useState)(!0);return{hasBlockingNotifications:s,isChecking:i,checkForBlockingNotifications:async()=>{try{n(!0);let s=await (0,a.iA)(e);t(s)}catch(e){console.error("Error checking for blocking notifications:",e),t(!1)}finally{n(!1)}},markAllAsRead:()=>{t(!1)}}}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27264:(e,s,t)=>{Promise.resolve().then(t.bind(t,82467))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},51013:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=t(65239),a=t(48088),i=t(88170),n=t.n(i),l=t(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let d={children:["",{children:["support",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,78076)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\support\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\support\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/support/page",pathname:"/support",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63712:(e,s,t)=>{Promise.resolve().then(t.bind(t,78076))},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78076:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\support\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\support\\page.tsx","default")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82467:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c});var r=t(60687),a=t(43210),i=t(85814),n=t.n(i),l=t(87979),o=t(744),d=t(98873);function c(){let{user:e,loading:s}=(0,l.Nu)(),{hasBlockingNotifications:t,isChecking:i,markAllAsRead:c}=(0,o.J)(e?.uid||null),[u,x]=(0,a.useState)(function(){let e=function(){let e=new Date,s=e.getHours(),t=e.getDay();if(!(t>=1&&t<=5)){let s=new Date(e);return s.setDate(e.getDate()+(0===t?1:8-t)),s.setHours(9,0,0,0),{isAvailable:!1,message:"Support is available Monday to Friday, 9 AM - 6 PM",nextAvailableTime:`Next available: ${s.toLocaleDateString()} at 9:00 AM`}}if(!(s>=9&&s<18))if(s<9)return new Date(e).setHours(9,0,0,0),{isAvailable:!1,message:"Support hours: 9 AM - 6 PM (Working days)",nextAvailableTime:"Available today at 9:00 AM"};else{let s=new Date(e);s.setDate(e.getDate()+1);let t=s.getDay();if(t>=1&&t<=5)return s.setHours(9,0,0,0),{isAvailable:!1,message:"Support hours: 9 AM - 6 PM (Working days)",nextAvailableTime:`Next available: ${s.toLocaleDateString()} at 9:00 AM`};{let e=new Date(s);return e.setDate(s.getDate()+(0===t?1:8-t)),e.setHours(9,0,0,0),{isAvailable:!1,message:"Support is available Monday to Friday, 9 AM - 6 PM",nextAvailableTime:`Next available: ${e.toLocaleDateString()} at 9:00 AM`}}}return{isAvailable:!0,message:"Support is currently available! We typically respond within minutes."}}();return{status:e.isAvailable?"online":"offline",message:e.message,nextAvailable:e.nextAvailableTime,hoursInfo:"Monday to Friday, 9:00 AM - 6:00 PM"}}());return s||i?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner mb-4"}),(0,r.jsx)("p",{className:"text-white",children:s?"Loading...":"Checking notifications..."})]})}):t&&e?(0,r.jsx)(d.A,{userId:e.uid,onAllRead:c}):(0,r.jsxs)("div",{className:"min-h-screen p-4",children:[(0,r.jsx)("header",{className:"glass-card p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)(n(),{href:"/dashboard",className:"glass-button px-4 py-2 text-white",children:[(0,r.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]}),(0,r.jsx)("h1",{className:"text-xl font-bold text-white",children:"Support & Help"}),(0,r.jsx)("div",{className:"w-24"})," "]})}),(0,r.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("h2",{className:"text-2xl font-bold text-white",children:[(0,r.jsx)("i",{className:"fas fa-headset mr-2"}),"Contact Support"]}),(0,r.jsxs)("div",{className:`px-3 py-1 rounded-full text-sm font-medium ${"online"===u.status?"bg-green-500/20 text-green-400 border border-green-500/30":"bg-red-500/20 text-red-400 border border-red-500/30"}`,children:[(0,r.jsx)("i",{className:`fas fa-circle mr-2 ${"online"===u.status?"text-green-400":"text-red-400"}`}),"online"===u.status?"Online":"Offline"]})]}),(0,r.jsxs)("div",{className:`p-4 rounded-lg mb-6 ${"online"===u.status?"bg-green-500/10 border border-green-500/20":"bg-orange-500/10 border border-orange-500/20"}`,children:[(0,r.jsxs)("p",{className:`font-medium mb-2 ${"online"===u.status?"text-green-400":"text-orange-400"}`,children:[(0,r.jsx)("i",{className:`fas ${"online"===u.status?"fa-check-circle":"fa-clock"} mr-2`}),u.message]}),u.nextAvailable&&(0,r.jsx)("p",{className:"text-white/60 text-sm",children:u.nextAvailable}),(0,r.jsxs)("p",{className:"text-white/60 text-sm mt-2",children:[(0,r.jsx)("i",{className:"fas fa-calendar mr-2"}),u.hoursInfo]})]}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"bg-green-500/20 border border-green-500/30 rounded-lg p-6",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("i",{className:"fab fa-whatsapp text-green-400 text-3xl mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-white font-bold text-lg",children:"WhatsApp Support"}),(0,r.jsx)("p",{className:"text-green-300 text-sm",children:"Instant messaging support"})]})]}),(0,r.jsx)("p",{className:"text-white/80 mb-4",children:"Get instant help via WhatsApp during business hours. Our team responds quickly to your queries on working days."}),(0,r.jsxs)("div",{className:`mb-3 p-2 rounded text-sm ${"online"===u.status?"bg-green-500/20 text-green-400":"bg-orange-500/20 text-orange-400"}`,children:[(0,r.jsx)("i",{className:`fas ${"online"===u.status?"fa-check":"fa-clock"} mr-2`}),"online"===u.status?"Available now - Usually responds within minutes":"Currently offline - Will respond when available"]}),(0,r.jsxs)("a",{href:"https://wa.me/917676636990",target:"_blank",rel:"noopener noreferrer",className:"btn-success bg-green-500 hover:bg-green-600 w-full",children:[(0,r.jsx)("i",{className:"fab fa-whatsapp mr-2"}),"Chat on WhatsApp: +91 7676636990"]})]}),(0,r.jsxs)("div",{className:"bg-blue-500/20 border border-blue-500/30 rounded-lg p-6",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("i",{className:"fas fa-envelope text-blue-400 text-3xl mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-white font-bold text-lg",children:"Email Support"}),(0,r.jsx)("p",{className:"text-blue-300 text-sm",children:"Detailed support via email"})]})]}),(0,r.jsx)("p",{className:"text-white/80 mb-4",children:"Send us detailed queries and we'll respond within 24 hours on working days."}),(0,r.jsxs)("div",{className:`mb-3 p-2 rounded text-sm ${"online"===u.status?"bg-blue-500/20 text-blue-400":"bg-orange-500/20 text-orange-400"}`,children:[(0,r.jsx)("i",{className:`fas ${"online"===u.status?"fa-check":"fa-clock"} mr-2`}),"online"===u.status?"Available now - Response within 24 hours":"Currently offline - Will respond on next working day"]}),(0,r.jsxs)("a",{href:"mailto:<EMAIL>",className:"btn-primary bg-blue-500 hover:bg-blue-600 w-full",children:[(0,r.jsx)("i",{className:"fas fa-envelope mr-2"}),"Email: <EMAIL>"]})]})]})]}),(0,r.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,r.jsxs)("h2",{className:"text-2xl font-bold text-white mb-6",children:[(0,r.jsx)("i",{className:"fas fa-question-circle mr-2"}),"Frequently Asked Questions"]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,r.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,r.jsx)("i",{className:"fas fa-play-circle mr-2 text-youtube-red"}),"How do I earn money by watching videos?"]}),(0,r.jsx)("p",{className:"text-white/80 text-sm",children:"Watch videos completely to earn money. You earn ₹10-400 per batch of 1000 videos depending on your plan. Videos must be watched for the full duration to count towards your earnings."})]}),(0,r.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,r.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,r.jsx)("i",{className:"fas fa-wallet mr-2 text-green-400"}),"When can I withdraw my earnings?"]}),(0,r.jsx)("p",{className:"text-white/80 text-sm",children:"Withdrawals are available between 10:00 AM to 6:00 PM on non-leave days. Minimum withdrawal is ₹50. Trial users cannot withdraw - upgrade to a paid plan first."})]}),(0,r.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,r.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,r.jsx)("i",{className:"fas fa-users mr-2 text-blue-400"}),"How does the referral system work?"]}),(0,r.jsx)("p",{className:"text-white/80 text-sm",children:"Share your referral code with friends. When they join and purchase a plan, you earn a bonus ranging from ₹50 to ₹1200 depending on the plan they choose."})]}),(0,r.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,r.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,r.jsx)("i",{className:"fas fa-crown mr-2 text-yellow-400"}),"What are the different plans available?"]}),(0,r.jsx)("p",{className:"text-white/80 text-sm",children:"We offer Trial (free), Starter (₹499), Basic (₹1499), Premium (₹2999), Gold (₹3999), Platinum (₹5999), and Diamond (₹9999) plans with different earning rates and video durations."})]}),(0,r.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,r.jsxs)("h3",{className:"text-white font-semibold mb-2",children:[(0,r.jsx)("i",{className:"fas fa-bolt mr-2 text-orange-400"}),"What is Quick Video Advantage?"]}),(0,r.jsx)("p",{className:"text-white/80 text-sm",children:"Admins can grant temporary quick video advantage that reduces your video duration to 30 seconds for a limited period, helping you complete tasks faster."})]})]})]}),(0,r.jsxs)("div",{className:"glass-card p-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,r.jsx)("i",{className:"fas fa-clock mr-2"}),"Support Hours"]}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-white font-semibold mb-2",children:"WhatsApp Support"}),(0,r.jsx)("p",{className:"text-white/80 text-sm mb-1",children:"Monday - Friday: 9:00 AM - 6:00 PM"}),(0,r.jsx)("p",{className:"text-white/60 text-sm mb-1",children:"Working days only"}),(0,r.jsx)("p",{className:"text-green-400 text-sm",children:"Usually responds within minutes during business hours"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-white font-semibold mb-2",children:"Email Support"}),(0,r.jsx)("p",{className:"text-white/80 text-sm mb-1",children:"Monday - Friday: 9:00 AM - 6:00 PM"}),(0,r.jsx)("p",{className:"text-white/60 text-sm mb-1",children:"Working days only"}),(0,r.jsx)("p",{className:"text-blue-400 text-sm",children:"Response within 24 hours on working days"})]})]})]})]})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},98873:(e,s,t)=>{"use strict";t.d(s,{A:()=>n});var r=t(60687),a=t(43210),i=t(3582);function n({userId:e,onAllRead:s}){let[t,n]=(0,a.useState)([]),[l,o]=(0,a.useState)(0),[d,c]=(0,a.useState)(!0),u=async()=>{let r=t[l];r?.id&&(await (0,i.bA)(r.id,e),l<t.length-1?o(l+1):s())};if(d)return(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",children:(0,r.jsx)("div",{className:"bg-white rounded-lg p-8 max-w-md w-full mx-4",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner w-8 h-8 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading notifications..."})]})})});if(0===t.length)return null;let x=t[l];return(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-2xl max-w-md w-full mx-4 overflow-hidden",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("i",{className:(e=>{switch(e){case"success":return"fas fa-check-circle text-green-500";case"warning":return"fas fa-exclamation-triangle text-yellow-500";case"error":return"fas fa-times-circle text-red-500";default:return"fas fa-info-circle text-blue-500"}})(x.type)}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-bold",children:"Important Notice"}),(0,r.jsxs)("p",{className:"text-blue-100 text-sm",children:[l+1," of ",t.length," notifications"]})]})]}),(0,r.jsx)("div",{className:"bg-white bg-opacity-20 rounded-full px-3 py-1",children:(0,r.jsx)("span",{className:"text-sm font-medium",children:"Required"})})]})}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("h4",{className:"text-xl font-bold text-gray-900 mb-3",children:x.title}),(0,r.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 mb-4",children:(0,r.jsx)("p",{className:"text-gray-800 leading-relaxed",children:x.message})}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-6",children:[(0,r.jsxs)("span",{children:["From: ",x.createdBy]}),(0,r.jsx)("span",{children:(e=>{let s=Math.floor((new Date().getTime()-e.getTime())/1e3);return s<60?"Just now":s<3600?`${Math.floor(s/60)} minutes ago`:s<86400?`${Math.floor(s/3600)} hours ago`:`${Math.floor(s/86400)} days ago`})(x.createdAt)})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm text-gray-600 mb-2",children:[(0,r.jsx)("span",{children:"Progress"}),(0,r.jsxs)("span",{children:[l+1,"/",t.length]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:{width:`${(l+1)/t.length*100}%`}})})]}),(0,r.jsxs)("button",{onClick:u,className:"w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2",children:[(0,r.jsx)("i",{className:"fas fa-check"}),(0,r.jsx)("span",{children:l<t.length-1?"Acknowledge & Continue":"Acknowledge & Proceed"})]})]}),(0,r.jsx)("div",{className:"bg-gray-50 px-6 py-4 border-t",children:(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-sm text-gray-600",children:[(0,r.jsx)("i",{className:"fas fa-info-circle"}),(0,r.jsx)("span",{children:"You must acknowledge all notifications to continue"})]})})]})})}}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[6204,6958,7567,8441,3582,7979],()=>t(51013));module.exports=r})();