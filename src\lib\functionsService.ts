// Client-side service for calling optimized Firebase Functions
// Reduces Firestore reads by using server-side processing

import { getFunctions, httpsCallable } from 'firebase/functions';
import app from './firebase';

// Initialize Functions
const functions = getFunctions(app);

// Types for function responses
interface DashboardData {
  user: {
    name: string;
    email: string;
    plan: string;
    wallet: number;
    activeDays: number;
    daysLeft: number;
    expired: boolean;
  };
  videos: {
    total: number;
    today: number;
    remaining: number;
  };
  status: {
    canWork: boolean;
    isAdminLeave: boolean;
    isUserLeave: boolean;
  };
  quickVideo: {
    remainingDays: number;
    active: boolean;
  };
}

interface VideoBatchResult {
  videosAdded: number;
  totalVideos: number;
  todayVideos: number;
  earnings: number;
  newWallet: number;
}

interface FunctionResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

// 🎯 OPTIMIZED: Get User Dashboard Data
// Replaces multiple client-side reads with single function call
export async function getUserDashboardData(): Promise<DashboardData> {
  try {
    console.log('📊 Calling optimized getUserDashboardData function...');
    
    const getUserDashboard = httpsCallable<{}, FunctionResponse<DashboardData>>(
      functions, 
      'getUserDashboardData'
    );
    
    const result = await getUserDashboard({});
    
    if (!result.data.success || !result.data.data) {
      throw new Error(result.data.error || 'Failed to get dashboard data');
    }
    
    console.log('✅ Dashboard data received from function');
    return result.data.data;
    
  } catch (error) {
    console.error('Error calling getUserDashboardData function:', error);
    throw error;
  }
}

// 🎯 OPTIMIZED: Submit Video Batch
// Replaces multiple reads/writes with single atomic function call
export async function submitVideoBatch(videosWatched: number = 1000): Promise<VideoBatchResult> {
  try {
    console.log(`🎬 Calling optimized submitVideoBatch function for ${videosWatched} videos...`);
    
    const submitBatch = httpsCallable<{ videosWatched: number }, FunctionResponse<VideoBatchResult>>(
      functions, 
      'submitVideoBatch'
    );
    
    const result = await submitBatch({ videosWatched });
    
    if (!result.data.success || !result.data.data) {
      throw new Error(result.data.error || 'Failed to submit video batch');
    }
    
    console.log('✅ Video batch submitted successfully via function');
    return result.data.data;
    
  } catch (error) {
    console.error('Error calling submitVideoBatch function:', error);
    throw error;
  }
}

// 🎯 OPTIMIZED: Get User Work Status
// Single function call to check if user can work today
export async function getUserWorkStatus(): Promise<{
  canWork: boolean;
  reason?: string;
  daysLeft: number;
  videosRemaining: number;
}> {
  try {
    console.log('🔍 Getting user work status via optimized function...');
    
    const dashboardData = await getUserDashboardData();
    
    let reason: string | undefined;
    if (!dashboardData.status.canWork) {
      if (dashboardData.user.expired) {
        reason = 'Plan expired';
      } else if (dashboardData.status.isAdminLeave) {
        reason = 'Admin leave day';
      } else if (dashboardData.status.isUserLeave) {
        reason = 'User leave day';
      } else {
        reason = 'Cannot work today';
      }
    }
    
    return {
      canWork: dashboardData.status.canWork,
      reason,
      daysLeft: dashboardData.user.daysLeft,
      videosRemaining: dashboardData.videos.remaining
    };
    
  } catch (error) {
    console.error('Error getting user work status:', error);
    throw error;
  }
}

// 🎯 CACHE INTEGRATION: Cached Dashboard Data
// Combines function optimization with client-side caching
let dashboardCache: {
  data: DashboardData | null;
  timestamp: number;
  ttl: number;
} = {
  data: null,
  timestamp: 0,
  ttl: 2 * 60 * 1000 // 2 minutes cache
};

export async function getCachedDashboardData(forceRefresh: boolean = false): Promise<DashboardData> {
  const now = Date.now();
  
  // Return cached data if valid and not forcing refresh
  if (!forceRefresh && 
      dashboardCache.data && 
      (now - dashboardCache.timestamp) < dashboardCache.ttl) {
    console.log('🎯 Using cached dashboard data');
    return dashboardCache.data;
  }
  
  // Fetch fresh data from function
  console.log('📡 Fetching fresh dashboard data from function');
  const data = await getUserDashboardData();
  
  // Update cache
  dashboardCache = {
    data,
    timestamp: now,
    ttl: 2 * 60 * 1000
  };
  
  return data;
}

// 🎯 UTILITY: Clear Dashboard Cache
// Call this after operations that change user data
export function clearDashboardCache(): void {
  console.log('🗑️ Clearing dashboard cache');
  dashboardCache = {
    data: null,
    timestamp: 0,
    ttl: 2 * 60 * 1000
  };
}

// 🎯 BATCH OPERATIONS: Multiple Function Calls
// For operations that need multiple function calls
export async function performBatchOperations(operations: Array<() => Promise<any>>): Promise<any[]> {
  try {
    console.log(`🔄 Performing ${operations.length} batch operations...`);
    
    const results = await Promise.all(operations);
    
    console.log('✅ All batch operations completed');
    return results;
    
  } catch (error) {
    console.error('Error in batch operations:', error);
    throw error;
  }
}

// 🎯 ERROR HANDLING: Retry Logic for Functions
export async function callFunctionWithRetry<T>(
  functionCall: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🔄 Function call attempt ${attempt}/${maxRetries}`);
      return await functionCall();
    } catch (error) {
      lastError = error as Error;
      console.warn(`❌ Function call attempt ${attempt} failed:`, error);
      
      if (attempt < maxRetries) {
        console.log(`⏳ Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        delay *= 2; // Exponential backoff
      }
    }
  }
  
  console.error(`💥 All ${maxRetries} function call attempts failed`);
  throw lastError!;
}

// 🎯 MONITORING: Function Call Statistics
interface FunctionStats {
  totalCalls: number;
  successfulCalls: number;
  failedCalls: number;
  averageResponseTime: number;
  lastCallTime: number;
}

const functionStats: Record<string, FunctionStats> = {};

export function getFunctionStats(): Record<string, FunctionStats> {
  return { ...functionStats };
}

export function resetFunctionStats(): void {
  Object.keys(functionStats).forEach(key => {
    delete functionStats[key];
  });
}

// Wrapper to track function performance
export async function trackFunctionCall<T>(
  functionName: string,
  functionCall: () => Promise<T>
): Promise<T> {
  const startTime = Date.now();
  
  if (!functionStats[functionName]) {
    functionStats[functionName] = {
      totalCalls: 0,
      successfulCalls: 0,
      failedCalls: 0,
      averageResponseTime: 0,
      lastCallTime: 0
    };
  }
  
  const stats = functionStats[functionName];
  stats.totalCalls++;
  stats.lastCallTime = startTime;
  
  try {
    const result = await functionCall();
    
    const responseTime = Date.now() - startTime;
    stats.successfulCalls++;
    stats.averageResponseTime = (
      (stats.averageResponseTime * (stats.successfulCalls - 1)) + responseTime
    ) / stats.successfulCalls;
    
    console.log(`📊 Function ${functionName} completed in ${responseTime}ms`);
    return result;
    
  } catch (error) {
    stats.failedCalls++;
    console.error(`💥 Function ${functionName} failed after ${Date.now() - startTime}ms:`, error);
    throw error;
  }
}
