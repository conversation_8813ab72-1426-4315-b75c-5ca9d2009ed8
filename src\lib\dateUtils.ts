/**
 * Utility functions for handling date conversions, especially Firestore Timestamps
 */

/**
 * Safely converts a Firestore Timestamp or any date-like value to a JavaScript Date object
 * @param dateValue - The date value to convert (Firestore Timestamp, Date, string, or number)
 * @returns A JavaScript Date object
 */
export function safeToDate(dateValue: any): Date {
  // Debug logging to see what we're receiving
  console.log('🔍 safeToDate input:', {
    value: dateValue,
    type: typeof dateValue,
    isDate: dateValue instanceof Date,
    hasToDate: dateValue && typeof dateValue === 'object' && typeof dateValue.toDate === 'function',
    isNull: dateValue === null,
    isUndefined: dateValue === undefined
  })

  // If it's already a Date object, return it
  if (dateValue instanceof Date) {
    console.log('✅ Returning existing Date object:', dateValue)
    return dateValue
  }

  // If it's a Firestore Timestamp with toDate method
  if (dateValue && typeof dateValue === 'object' && typeof dateValue.toDate === 'function') {
    try {
      const converted = dateValue.toDate()
      console.log('✅ Converted Firestore Timestamp:', converted)
      return converted
    } catch (error) {
      console.warn('❌ Failed to convert Firestore Timestamp:', error)
      return new Date() // Fallback to current date only for conversion errors
    }
  }

  // If it's a valid date string or number
  if (dateValue && (typeof dateValue === 'string' || typeof dateValue === 'number')) {
    const parsed = new Date(dateValue)
    if (!isNaN(parsed.getTime())) {
      console.log('✅ Parsed string/number to date:', parsed)
      return parsed
    }
  }

  // For null/undefined or invalid values, return current date as fallback
  console.warn('⚠️ Using current date fallback for:', dateValue)
  return new Date()
}

/**
 * Formats a date for display in local format
 * @param dateValue - The date value to format
 * @returns Formatted date string
 */
export function formatDisplayDate(dateValue: any): string {
  const date = safeToDate(dateValue)
  return date.toLocaleDateString()
}

/**
 * Formats a time for display in local format
 * @param dateValue - The date value to format
 * @returns Formatted time string
 */
export function formatDisplayTime(dateValue: any): string {
  const date = safeToDate(dateValue)
  return date.toLocaleTimeString()
}

/**
 * Formats a date and time for display in local format
 * @param dateValue - The date value to format
 * @returns Formatted date and time string
 */
export function formatDisplayDateTime(dateValue: any): string {
  const date = safeToDate(dateValue)
  return `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`
}

/**
 * Converts an array of objects with date fields to ensure all dates are JavaScript Date objects
 * @param items - Array of objects that may contain date fields
 * @param dateFields - Array of field names that contain dates (defaults to common date field names)
 * @returns Array with converted date fields
 */
export function convertDatesInArray<T>(items: T[], dateFields: string[] = ['date', 'createdAt', 'updatedAt', 'requestDate']): T[] {
  return items.map(item => {
    const converted = { ...item } as any

    dateFields.forEach(field => {
      if (converted[field]) {
        converted[field] = safeToDate(converted[field])
      }
    })

    return converted
  })
}

/**
 * Safely formats a string field by ensuring it's not null/undefined
 * @param value - The string value to format
 * @param defaultValue - Default value if the input is null/undefined
 * @returns Formatted string
 */
export function safeStringFormat(value: any, defaultValue: string = 'Unknown'): string {
  if (!value || typeof value !== 'string') {
    return defaultValue
  }
  return value
}

/**
 * Safely capitalizes the first letter of a string
 * @param value - The string to capitalize
 * @param defaultValue - Default value if the input is null/undefined
 * @returns Capitalized string
 */
export function safeCapitalize(value: any, defaultValue: string = 'Unknown'): string {
  const str = safeStringFormat(value, defaultValue)
  if (str === defaultValue) {
    return str
  }
  return str.charAt(0).toUpperCase() + str.slice(1)
}
