'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useAuthState } from '@/hooks/useAuth'
import Swal from 'sweetalert2'

interface Plan {
  id: string
  name: string
  price: number
  duration: number
  earningPerVideo: number
  videoDuration: number
  features: string[]
  popular?: boolean
}

const plans: Plan[] = [
  {
    id: 'trial',
    name: 'Trial',
    price: 0,
    duration: 2,
    earningPerVideo: 10,
    videoDuration: 30, // 30 seconds
    features: [
      '2 days access',
      '₹10 per batch (1000 videos)',
      'Basic support',
      'Video duration: 30 seconds'
    ]
  },
  {
    id: 'starter',
    name: 'Starter',
    price: 499,
    duration: 30,
    earningPerVideo: 25,
    videoDuration: 30, // 30 seconds
    features: [
      '30 days access',
      '₹25 per batch (1000 videos)',
      'Priority support',
      'Referral bonus: ₹50',
      'Video duration: 30 seconds'
    ]
  },
  {
    id: 'basic',
    name: 'Basic',
    price: 1499,
    duration: 30,
    earningPerVideo: 75,
    videoDuration: 30, // 30 seconds
    features: [
      '30 days access',
      '₹75 per batch (1000 videos)',
      'Priority support',
      'Referral bonus: ₹150',
      '1000 videos credited to total count',
      'Video duration: 30 seconds'
    ],
    popular: true
  },
  {
    id: 'premium',
    name: 'Premium',
    price: 2999,
    duration: 30,
    earningPerVideo: 150,
    videoDuration: 30, // 30 seconds
    features: [
      '30 days access',
      '₹150 per batch (1000 videos)',
      'Premium support',
      'Referral bonus: ₹300',
      '1000 videos credited to total count',
      'Video duration: 30 seconds'
    ]
  },
  {
    id: 'gold',
    name: 'Gold',
    price: 3999,
    duration: 30,
    earningPerVideo: 200,
    videoDuration: 30, // 30 seconds
    features: [
      '30 days access',
      '₹200 per batch (1000 videos)',
      'Premium support',
      'Referral bonus: ₹400',
      '1000 videos credited to total count',
      'Video duration: 30 seconds',
      'Priority customer support'
    ]
  },
  {
    id: 'platinum',
    name: 'Platinum',
    price: 5999,
    duration: 30,
    earningPerVideo: 250,
    videoDuration: 30, // 30 seconds
    features: [
      '30 days access',
      '₹250 per batch (1000 videos)',
      'Premium support',
      'Referral bonus: ₹700',
      '1000 videos credited to total count',
      'Video duration: 30 seconds',
      'Dedicated account manager',
      'Early access to new features'
    ]
  },
  {
    id: 'diamond',
    name: 'Diamond',
    price: 9999,
    duration: 30,
    earningPerVideo: 400,
    videoDuration: 30, // 30 seconds
    features: [
      '30 days access',
      '₹400 per batch (1000 videos)',
      'VIP support',
      'Referral bonus: ₹1200',
      '1000 videos credited to total count',
      'Video duration: 30 seconds',
      'Dedicated account manager',
      'Early access to new features',
      'Exclusive earning opportunities'
    ]
  }
]

export default function PlansPage() {
  const { user, loading } = useAuthState()
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)

  const handlePlanSelect = async (plan: Plan) => {
    if (!user) {
      Swal.fire({
        icon: 'info',
        title: 'Login Required',
        text: 'Please login to purchase a plan',
        showCancelButton: true,
        confirmButtonText: 'Login',
        cancelButtonText: 'Cancel'
      }).then((result) => {
        if (result.isConfirmed) {
          window.location.href = '/login'
        }
      })
      return
    }

    if (plan.id === 'trial') {
      Swal.fire({
        icon: 'info',
        title: 'Trial Plan',
        text: 'You are already on the trial plan. Upgrade to a paid plan for better earnings!',
      })
      return
    }

    setSelectedPlan(plan.id)
    setIsProcessing(true)

    try {
      // In a real implementation, this would integrate with a payment gateway
      // For now, we'll just show a message
      await new Promise(resolve => setTimeout(resolve, 2000)) // Simulate processing

      Swal.fire({
        icon: 'info',
        title: 'Payment Integration Required',
        html: `
          <p>To complete your purchase of the <strong>${plan.name}</strong> plan (₹${plan.price}), please contact our support team.</p>
          <br>
          <p><strong>Plan Details:</strong></p>
          <ul style="text-align: left; margin: 10px 0;">
            <li>Duration: ${plan.duration} days</li>
            <li>Earning: ₹${plan.earningPerVideo} per 1000 videos</li>
            <li>Video duration: ${plan.videoDuration < 60 ? `${plan.videoDuration} seconds` : `${Math.floor(plan.videoDuration / 60)} minute${plan.videoDuration >= 120 ? 's' : ''}`}</li>
          </ul>
          <br>
          <p><strong>Contact Options:</strong></p>
          <p>📧 Email: <strong><EMAIL></strong></p>
        `,
        confirmButtonText: 'Contact Support',
        showCancelButton: true,
        cancelButtonText: 'Cancel'
      })
    } catch (error) {
      console.error('Error processing plan selection:', error)
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Failed to process plan selection. Please try again.',
      })
    } finally {
      setIsProcessing(false)
      setSelectedPlan(null)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="spinner"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen p-4">
      {/* Header */}
      <header className="glass-card p-4 mb-6">
        <div className="flex items-center justify-between">
          <Link href={user ? "/dashboard" : "/"} className="glass-button px-4 py-2 text-white">
            <i className="fas fa-arrow-left mr-2"></i>
            Back
          </Link>
          <h1 className="text-xl font-bold text-white">Choose Your Plan</h1>
          <div className="w-20"></div> {/* Spacer for centering */}
        </div>
      </header>

      {/* Plans Grid */}
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Start Earning with MyTube
          </h2>
          <p className="text-white/80 text-lg max-w-2xl mx-auto">
            Choose the perfect plan for your earning goals. Watch videos and earn money with our flexible pricing options.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-6">
          {plans.map((plan) => (
            <div
              key={plan.id}
              className={`glass-card p-8 relative ${
                plan.popular ? 'ring-2 ring-yellow-400' : ''
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-yellow-400 text-black px-4 py-1 rounded-full text-sm font-bold">
                    Most Popular
                  </span>
                </div>
              )}

              <div className="text-center mb-6">
                <h3 className="text-2xl font-bold text-white mb-2">{plan.name}</h3>
                <div className="mb-4">
                  <span className="text-4xl font-bold text-white">₹{plan.price}</span>
                  {plan.price > 0 && (
                    <span className="text-white/60 ml-2">/ {plan.duration} days</span>
                  )}
                </div>
                <p className="text-green-400 font-semibold">
                  Earn ₹{plan.earningPerVideo} per 1000 videos
                </p>
              </div>

              <ul className="space-y-3 mb-8">
                {plan.features.map((feature, index) => (
                  <li key={index} className="flex items-center text-white/80">
                    <i className="fas fa-check text-green-400 mr-3"></i>
                    {feature}
                  </li>
                ))}
              </ul>

              <button
                onClick={() => handlePlanSelect(plan)}
                disabled={isProcessing && selectedPlan === plan.id}
                className={`w-full py-3 rounded-lg font-semibold transition-all duration-300 ${
                  plan.popular
                    ? 'bg-yellow-400 text-black hover:bg-yellow-500'
                    : plan.price === 0
                    ? 'bg-gray-600 text-white hover:bg-gray-700'
                    : 'bg-youtube-red text-white hover:bg-red-700'
                } disabled:opacity-50`}
              >
                {isProcessing && selectedPlan === plan.id ? (
                  <>
                    <div className="spinner mr-2 w-5 h-5 inline-block"></div>
                    Processing...
                  </>
                ) : plan.price === 0 ? (
                  'Current Plan'
                ) : (
                  `Choose ${plan.name}`
                )}
              </button>
            </div>
          ))}
        </div>

        {/* Additional Information */}
        <div className="mt-12 glass-card p-8">
          <h3 className="text-xl font-bold text-white mb-4 text-center">
            <i className="fas fa-info-circle mr-2"></i>
            Plan Benefits Explained
          </h3>

          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <h4 className="text-lg font-semibold text-white mb-3">Earning Structure</h4>
              <ul className="space-y-2 text-white/80">
                <li>• Watch 1000 videos daily to earn the full amount</li>
                <li>• Each video must be watched for the full duration</li>
                <li>• Earnings are credited to your earning wallet</li>
                <li>• Transfer earnings to main wallet for withdrawal</li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold text-white mb-3">Video Duration Benefits</h4>
              <ul className="space-y-2 text-white/80">
                <li>• All plans now have 30-second video duration</li>
                <li>• Consistent video duration across all plans</li>
                <li>• 1000 videos per day for maximum earnings</li>
                <li>• All videos must be watched completely</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div className="mt-8 text-center">
          <p className="text-white/60 mb-4">
            Need help choosing a plan? Contact us during business hours (9 AM - 6 PM, working days):
          </p>
          <div className="flex justify-center">
            <a
              href="mailto:<EMAIL>"
              className="flex items-center text-white hover:text-blue-400 transition-colors"
            >
              <i className="fas fa-envelope mr-2"></i>
              <EMAIL>
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
