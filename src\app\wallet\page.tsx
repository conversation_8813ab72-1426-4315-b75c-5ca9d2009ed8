'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRequireAuth } from '@/hooks/useAuth'
import { useBlockingNotifications } from '@/hooks/useBlockingNotifications'
import { useLeaveMonitor } from '@/hooks/useLeaveMonitor'
import { getWalletData, getBankDetails, saveBankDetails, BankDetails, createWithdrawalRequest, getUserWithdrawals, checkWithdrawalAllowed, getUserData } from '@/lib/dataService'
import { optimizedService } from '@/lib/optimizedDataService'
import { formatDisplayDate, formatDisplayTime, safeToDate } from '@/lib/dateUtils'
import BlockingNotificationModal from '@/components/BlockingNotificationModal'
import Swal from 'sweetalert2'

interface WalletData {
  wallet: number
}

interface Transaction {
  id: string
  type: string
  amount: number
  description: string
  date: Date
  status: string
}

export default function WalletPage() {
  const { user, loading } = useRequireAuth()
  const { hasBlockingNotifications, isChecking, markAllAsRead } = useBlockingNotifications(user?.uid || null)
  const { isBlocked: isLeaveBlocked, leaveStatus } = useLeaveMonitor({
    userId: user?.uid || null,
    checkInterval: 30000, // Check every 30 seconds
    enabled: !!user
  })
  const [walletData, setWalletData] = useState<WalletData | null>(null)
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [dataLoading, setDataLoading] = useState(true)
  const [withdrawAmount, setWithdrawAmount] = useState('')
  const [isWithdrawing, setIsWithdrawing] = useState(false)

  // Bank details state
  const [bankDetails, setBankDetails] = useState<BankDetails | null>(null)
  const [showBankForm, setShowBankForm] = useState(false)
  const [bankFormData, setBankFormData] = useState<BankDetails>({
    accountHolderName: '',
    accountNumber: '',
    ifscCode: '',
    bankName: ''
  })
  const [isSavingBank, setIsSavingBank] = useState(false)
  const [withdrawalAllowed, setWithdrawalAllowed] = useState<{ allowed: boolean; reason?: string }>({ allowed: true })
  const [checkingWithdrawal, setCheckingWithdrawal] = useState(false)
  const [userData, setUserData] = useState<any>(null)

  useEffect(() => {
    // NOTE: Daily process is now handled by Firebase Functions scheduled task
    // Removed client-side daily increment to prevent multiple increments per day

    if (user) {
      loadWalletData()
      loadBankDetails()
      loadUserData()
      checkWithdrawalEligibility()
    }
  }, [user])

  // Monitor leave status changes and update withdrawal eligibility
  useEffect(() => {
    if (isLeaveBlocked) {
      setWithdrawalAllowed({
        allowed: false,
        reason: leaveStatus.reason || 'Withdrawals are not available due to leave.'
      })
    } else if (user) {
      // Re-check withdrawal eligibility when leave status changes
      checkWithdrawalEligibility()
    }
  }, [isLeaveBlocked, leaveStatus, user])

  const loadUserData = async () => {
    try {
      const data = await getUserData(user!.uid)
      setUserData(data)
    } catch (error) {
      console.error('Error loading user data:', error)
    }
  }

  const checkWithdrawalEligibility = async () => {
    if (!user) return

    try {
      setCheckingWithdrawal(true)
      const result = await checkWithdrawalAllowed(user.uid)
      setWithdrawalAllowed(result)
    } catch (error) {
      console.error('Error checking withdrawal eligibility:', error)
      setWithdrawalAllowed({
        allowed: false,
        reason: 'Unable to verify withdrawal eligibility. Please try again.'
      })
    } finally {
      setCheckingWithdrawal(false)
    }
  }

  const loadWalletData = async () => {
    try {
      setDataLoading(true)

      // Use optimized functions for better performance
      try {
        console.log('🚀 Loading wallet data with optimized functions...')
        const [walletResult, transactionsResult] = await Promise.all([
          getWalletData(user!.uid),
          optimizedService.getUserTransactions(user!.uid, 20, 'withdrawal_request')
        ])

        setWalletData(walletResult)

        // Convert transactions to expected format
        const formattedTransactions = transactionsResult.map((transaction: any) => ({
          id: transaction.id,
          type: transaction.type,
          amount: transaction.type === 'withdrawal_request' ? -Math.abs(transaction.amount) : transaction.amount,
          description: transaction.description,
          date: safeToDate(transaction.date),
          status: transaction.status || 'completed'
        }))

        setTransactions(formattedTransactions as Transaction[])
        console.log('✅ Wallet data loaded via optimized functions', formattedTransactions)

      } catch (optimizedError) {
        console.warn('⚠️ Optimized functions failed, using fallback:', optimizedError)

        // Fallback to original method
        const [walletResult, withdrawalsResult] = await Promise.all([
          getWalletData(user!.uid),
          getUserWithdrawals(user!.uid, 20)
        ])

        setWalletData(walletResult)
        // Convert withdrawals to transaction format for display
        const withdrawalTransactions = withdrawalsResult.map((withdrawal: any) => ({
          id: withdrawal.id,
          type: 'withdrawal',
          amount: -withdrawal.amount, // Negative to show as debit
          description: `Withdrawal request - ₹${withdrawal.amount}`,
          date: safeToDate(withdrawal.date),
          status: withdrawal.status
        }))
        setTransactions(withdrawalTransactions as Transaction[])
        console.log('✅ Wallet data loaded via fallback method', withdrawalTransactions)
      }
    } catch (error) {
      console.error('Error loading wallet data:', error)
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Failed to load wallet data. Please try again.',
      })
    } finally {
      setDataLoading(false)
    }
  }

  const loadBankDetails = async () => {
    try {
      const bankData = await getBankDetails(user!.uid)
      setBankDetails(bankData)
      if (bankData) {
        setBankFormData(bankData)
      }
    } catch (error) {
      console.error('Error loading bank details:', error)
    }
  }

  const handleBankFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Prevent multiple submissions
    if (isSavingBank) return

    try {
      setIsSavingBank(true)
      await saveBankDetails(user!.uid, bankFormData)

      setBankDetails(bankFormData)
      setShowBankForm(false)

      Swal.fire({
        icon: 'success',
        title: 'Bank Details Saved',
        text: 'Your bank details have been saved successfully',
        timer: 2000,
        showConfirmButton: false
      })
    } catch (error: any) {
      console.error('Error saving bank details:', error)
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: error.message || 'Failed to save bank details. Please try again.',
      })
    } finally {
      setIsSavingBank(false)
    }
  }

  const handleBankFormChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setBankFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleWithdraw = async () => {
    // Prevent multiple clicks with strict checking
    if (isWithdrawing) {
      console.log('⚠️ Withdrawal already in progress, ignoring duplicate request')
      return
    }

    // Check if plan is expired based on active days
    try {
      const { isUserPlanExpired } = await import('@/lib/dataService')
      const planStatus = await isUserPlanExpired(user!.uid)

      if (planStatus.expired) {
        Swal.fire({
          icon: 'error',
          title: 'Plan Expired',
          html: `
            <div class="text-center">
              <p class="mb-2">Your plan has expired and you cannot make withdrawals.</p>
              <p class="text-sm text-gray-600 mb-2">${planStatus.reason}</p>
              <p class="text-sm text-blue-600">Please upgrade your plan to continue using withdrawal services.</p>
            </div>
          `,
          confirmButtonText: 'Go to Plans',
          showCancelButton: true,
          cancelButtonText: 'OK'
        }).then((result) => {
          if (result.isConfirmed) {
            window.location.href = '/plans'
          }
        })
        return
      }
    } catch (error) {
      console.error('Error checking plan expiry:', error)
    }

    // Check if withdrawals are blocked due to leave
    if (isLeaveBlocked) {
      Swal.fire({
        icon: 'warning',
        title: 'Withdrawal Not Available',
        text: leaveStatus.reason || 'Withdrawals are not available due to leave.',
        confirmButtonText: 'OK'
      })
      return
    }

    const amount = parseFloat(withdrawAmount)

    if (!amount || amount <= 0) {
      Swal.fire({
        icon: 'error',
        title: 'Invalid Amount',
        text: 'Please enter a valid amount to withdraw',
      })
      return
    }

    if (amount < 50) {
      Swal.fire({
        icon: 'error',
        title: 'Minimum Withdrawal',
        text: 'Minimum withdrawal amount is ₹50',
      })
      return
    }

    if (amount > (walletData?.wallet || 0)) {
      Swal.fire({
        icon: 'error',
        title: 'Insufficient Balance',
        text: 'You do not have enough balance in your wallet',
      })
      return
    }

    if (!bankDetails) {
      Swal.fire({
        icon: 'warning',
        title: 'Bank Details Required',
        text: 'Please add your bank details before making a withdrawal',
      })
      return
    }

    try {
      // Set loading state immediately to prevent race conditions
      setIsWithdrawing(true)
      console.log(`🔄 Processing withdrawal request for ₹${amount}`)

      // Create withdrawal request in Firestore with atomic operations
      await createWithdrawalRequest(user!.uid, amount, bankDetails)

      // Reload wallet data to show updated transactions
      await loadWalletData()

      Swal.fire({
        icon: 'success',
        title: 'Withdrawal Request Submitted',
        text: `Your withdrawal request for ₹${amount} has been submitted and will be processed within 24-48 hours.`,
      })

      setWithdrawAmount('')
      console.log(`✅ Withdrawal request completed successfully`)
    } catch (error: any) {
      console.error('❌ Error processing withdrawal:', error)
      Swal.fire({
        icon: 'error',
        title: 'Withdrawal Failed',
        text: error.message || 'Failed to process withdrawal request. Please try again.',
      })
    } finally {
      setIsWithdrawing(false)
    }
  }

  const formatCurrency = (amount: number | undefined) => {
    if (amount === undefined || amount === null || isNaN(amount)) {
      return '₹0.00'
    }
    return `₹${amount.toFixed(2)}`
  }



  if (loading || dataLoading || isChecking) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="spinner mb-4"></div>
          <p className="text-white">
            {loading ? 'Loading...' : isChecking ? 'Checking notifications...' : 'Loading wallet...'}
          </p>
        </div>
      </div>
    )
  }

  // Show blocking notifications if any exist
  if (hasBlockingNotifications && user) {
    return (
      <BlockingNotificationModal
        userId={user.uid}
        onAllRead={markAllAsRead}
      />
    )
  }

  return (
    <div className="min-h-screen p-4">
      {/* Header */}
      <header className="glass-card p-4 mb-6">
        <div className="flex items-center justify-between">
          <Link href="/dashboard" className="glass-button px-4 py-2 text-white">
            <i className="fas fa-arrow-left mr-2"></i>
            Back to Dashboard
          </Link>
          <h1 className="text-xl font-bold text-white">My Wallet</h1>
          <button
            onClick={loadWalletData}
            className="glass-button px-4 py-2 text-white"
          >
            <i className="fas fa-sync-alt mr-2"></i>
            Refresh
          </button>
        </div>
      </header>

      {/* Single Wallet Balance */}
      <div className="glass-card p-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-2xl font-semibold text-white">My Wallet</h3>
          <i className="fas fa-wallet text-green-400 text-3xl"></i>
        </div>
        <p className="text-4xl font-bold text-green-400 mb-2">
          {formatCurrency(walletData?.wallet || 0)}
        </p>
        <p className="text-white/60">Total available balance</p>
      </div>

      {/* Bank Details Section */}
      <div className="glass-card p-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-bold text-white">
            <i className="fas fa-university mr-2"></i>
            Bank Details
          </h3>
          {bankDetails && !showBankForm && (
            <button
              onClick={() => setShowBankForm(true)}
              className="glass-button px-4 py-2 text-white"
            >
              <i className="fas fa-edit mr-2"></i>
              Edit
            </button>
          )}
        </div>

        {!bankDetails && !showBankForm ? (
          <div className="text-center py-6">
            <i className="fas fa-university text-white/30 text-4xl mb-4"></i>
            <p className="text-white/60 mb-4">No bank details added yet</p>
            <button
              onClick={() => setShowBankForm(true)}
              className="btn-primary"
            >
              <i className="fas fa-plus mr-2"></i>
              Add Bank Details
            </button>
          </div>
        ) : showBankForm ? (
          <form onSubmit={handleBankFormSubmit} className="space-y-4">
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <label className="block text-white font-medium mb-2">
                  Account Holder Name *
                </label>
                <input
                  type="text"
                  name="accountHolderName"
                  value={bankFormData.accountHolderName}
                  onChange={handleBankFormChange}
                  className="form-input"
                  placeholder="Enter account holder name"
                  required
                />
              </div>
              <div>
                <label className="block text-white font-medium mb-2">
                  Bank Name *
                </label>
                <input
                  type="text"
                  name="bankName"
                  value={bankFormData.bankName}
                  onChange={handleBankFormChange}
                  className="form-input"
                  placeholder="Enter bank name"
                  required
                />
              </div>
              <div>
                <label className="block text-white font-medium mb-2">
                  Account Number *
                </label>
                <input
                  type="text"
                  name="accountNumber"
                  value={bankFormData.accountNumber}
                  onChange={handleBankFormChange}
                  className="form-input"
                  placeholder="Enter account number"
                  required
                />
              </div>
              <div>
                <label className="block text-white font-medium mb-2">
                  IFSC Code *
                </label>
                <input
                  type="text"
                  name="ifscCode"
                  value={bankFormData.ifscCode}
                  onChange={handleBankFormChange}
                  className="form-input"
                  placeholder="Enter IFSC code (e.g., SBIN0001234)"
                  required
                />
              </div>
            </div>
            <div className="flex gap-4">
              <button
                type="submit"
                disabled={isSavingBank}
                className={`${isSavingBank ? 'btn-disabled cursor-not-allowed opacity-50' : 'btn-primary hover:bg-blue-600'}`}
              >
                {isSavingBank ? (
                  <>
                    <div className="spinner mr-2 w-5 h-5"></div>
                    Saving Bank Details...
                  </>
                ) : (
                  <>
                    <i className="fas fa-save mr-2"></i>
                    Save Bank Details
                  </>
                )}
              </button>
              <button
                type="button"
                onClick={() => setShowBankForm(false)}
                className="btn-secondary"
              >
                Cancel
              </button>
            </div>
          </form>
        ) : (
          <div className="bg-white/10 rounded-lg p-4">
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <p className="text-white/60 text-sm">Account Holder</p>
                <p className="text-white font-medium">{bankDetails?.accountHolderName}</p>
              </div>
              <div>
                <p className="text-white/60 text-sm">Bank Name</p>
                <p className="text-white font-medium">{bankDetails?.bankName}</p>
              </div>
              <div>
                <p className="text-white/60 text-sm">Account Number</p>
                <p className="text-white font-medium">
                  ****{bankDetails?.accountNumber.slice(-4)}
                </p>
              </div>
              <div>
                <p className="text-white/60 text-sm">IFSC Code</p>
                <p className="text-white font-medium">{bankDetails?.ifscCode}</p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Withdrawal Section */}
      <div className="glass-card p-6 mb-6">
        <h3 className="text-lg font-bold text-white mb-4">
          <i className="fas fa-money-bill-wave mr-2"></i>
          Withdraw Funds
        </h3>

        {/* Trial Plan Warning */}
        {userData?.plan === 'Trial' && (
          <div className="mb-4 p-4 bg-red-500/20 border border-red-500/30 rounded-lg">
            <div className="flex items-center mb-2">
              <i className="fas fa-lock text-red-400 mr-2"></i>
              <span className="text-red-400 font-medium">Trial Plan Restriction</span>
            </div>
            <p className="text-white/80 text-sm mb-3">
              Trial plan users cannot make withdrawals. Please upgrade to a paid plan to enable withdrawal functionality.
            </p>
            <div className="flex gap-3">
              <Link href="/plans" className="btn-primary inline-block">
                <i className="fas fa-arrow-up mr-2"></i>
                Upgrade Plan
              </Link>

            </div>
          </div>
        )}

        {/* Withdrawal Timing Info */}
        <div className="mb-4 p-4 bg-blue-500/20 border border-blue-500/30 rounded-lg">
          <div className="flex items-center mb-2">
            <i className="fas fa-clock text-blue-400 mr-2"></i>
            <span className="text-blue-400 font-medium">Withdrawal Timings</span>
          </div>
          <p className="text-white/80 text-sm mb-2">
            Withdrawals are only allowed between <strong>10:00 AM to 6:00 PM</strong> on non-leave days.
          </p>
          <div className="flex items-center justify-between">
            <p className="text-white/60 text-xs">
              Current time: {new Date().toLocaleTimeString()} |
              Status: {withdrawalAllowed.allowed ? (
                <span className="text-green-400 font-medium">✓ Available</span>
              ) : (
                <span className="text-red-400 font-medium">✗ Not Available</span>
              )}
            </p>
            <button
              onClick={checkWithdrawalEligibility}
              disabled={checkingWithdrawal}
              className="text-blue-400 hover:text-blue-300 text-xs"
            >
              {checkingWithdrawal ? (
                <div className="spinner w-3 h-3"></div>
              ) : (
                <i className="fas fa-sync-alt"></i>
              )}
            </button>
          </div>
          {!withdrawalAllowed.allowed && withdrawalAllowed.reason && (
            <p className="text-red-400 text-sm mt-2">
              <i className="fas fa-exclamation-triangle mr-1"></i>
              {withdrawalAllowed.reason}
            </p>
          )}
        </div>

        {!bankDetails ? (
          <div className="text-center py-6">
            <i className="fas fa-exclamation-triangle text-yellow-400 text-3xl mb-4"></i>
            <p className="text-white/60 mb-4">Please add your bank details before making a withdrawal</p>
            <button
              onClick={() => setShowBankForm(true)}
              className="btn-primary"
            >
              <i className="fas fa-university mr-2"></i>
              Add Bank Details
            </button>
          </div>
        ) : (
          <>
            <div className="flex flex-col sm:flex-row gap-4">
              <input
                type="number"
                value={withdrawAmount}
                onChange={(e) => setWithdrawAmount(e.target.value)}
                placeholder="Enter amount to withdraw (Min: ₹50)"
                className="form-input flex-1"
                min="50"
                max={walletData?.wallet || 0}
              />
              <button
                onClick={handleWithdraw}
                disabled={isWithdrawing || !withdrawAmount || !withdrawalAllowed.allowed || parseFloat(withdrawAmount) <= 0}
                className={`whitespace-nowrap ${
                  isWithdrawing
                    ? 'btn-disabled cursor-not-allowed opacity-50 pointer-events-none'
                    : withdrawalAllowed.allowed && withdrawAmount && parseFloat(withdrawAmount) > 0
                      ? 'btn-primary hover:bg-blue-600'
                      : 'btn-disabled cursor-not-allowed opacity-50'
                }`}
                style={{ pointerEvents: isWithdrawing ? 'none' : 'auto' }}
              >
                {isWithdrawing ? (
                  <>
                    <div className="spinner mr-2 w-5 h-5"></div>
                    Processing Withdrawal...
                  </>
                ) : (
                  <>
                    <i className="fas fa-download mr-2"></i>
                    Withdraw ₹{withdrawAmount || '0'}
                  </>
                )}
              </button>
            </div>
            <p className="text-white/60 text-sm mt-2">
              Available: {formatCurrency(walletData?.wallet || 0)} | Minimum: ₹50
            </p>
          </>
        )}
      </div>

      {/* Withdrawal History */}
      <div className="glass-card p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-bold text-white">
            <i className="fas fa-money-bill-wave mr-2"></i>
            Withdrawal History
          </h3>
          <button
            onClick={loadWalletData}
            className="glass-button px-4 py-2 text-white"
          >
            <i className="fas fa-sync-alt mr-2"></i>
            Refresh
          </button>
        </div>

        {transactions.length === 0 ? (
          <div className="text-center py-8">
            <i className="fas fa-money-bill-wave text-white/30 text-4xl mb-4"></i>
            <p className="text-white/60 mb-2">No withdrawal requests yet</p>
            <p className="text-white/40 text-sm">Your withdrawal requests will appear here</p>
          </div>
        ) : (
          <div className="space-y-3">
            {transactions.map((transaction) => (
              <div
                key={transaction.id}
                className="flex items-center justify-between p-4 bg-white/10 rounded-lg"
              >
                <div className="flex items-center">
                  <i className="fas fa-money-bill-wave text-red-400 mr-3"></i>
                  <div>
                    <p className="text-white font-medium">{transaction.description}</p>
                    <p className="text-white/60 text-sm">
                      {formatDisplayDate(transaction.date)} at {formatDisplayTime(transaction.date)}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-bold text-red-400">
                    {formatCurrency(Math.abs(transaction.amount))}
                  </p>
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    transaction.status === 'pending' ? 'bg-yellow-500/20 text-yellow-400' :
                    transaction.status === 'approved' ? 'bg-green-500/20 text-green-400' :
                    transaction.status === 'rejected' ? 'bg-red-500/20 text-red-400' :
                    transaction.status === 'completed' ? 'bg-blue-500/20 text-blue-400' :
                    'bg-gray-500/20 text-gray-400'
                  }`}>
                    {transaction.status === 'pending' ? '⏳ Pending' :
                     transaction.status === 'approved' ? '✅ Approved' :
                     transaction.status === 'rejected' ? '❌ Rejected' :
                     transaction.status === 'completed' ? '✅ Completed' :
                     transaction.status}
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>


    </div>
  )
}
