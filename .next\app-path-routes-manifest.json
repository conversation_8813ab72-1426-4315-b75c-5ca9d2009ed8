{"/_not-found/page": "/_not-found", "/admin/daily-active-days/page": "/admin/daily-active-days", "/admin/fix-active-days/page": "/admin/fix-active-days", "/admin/fix-plan-expiry/page": "/admin/fix-plan-expiry", "/admin/notifications/page": "/admin/notifications", "/admin/login/page": "/admin/login", "/admin/settings/page": "/admin/settings", "/admin/fix-permissions/page": "/admin/fix-permissions", "/admin/setup/page": "/admin/setup", "/admin/simple-upload/page": "/admin/simple-upload", "/admin/test-blocking/page": "/admin/test-blocking", "/admin/transactions/page": "/admin/transactions", "/admin/page": "/admin", "/admin/reset-daily-tracking/page": "/admin/reset-daily-tracking", "/clear-cache/page": "/clear-cache", "/admin/upload-users/page": "/admin/upload-users", "/admin/leaves/page": "/admin/leaves", "/debug-active-days/page": "/debug-active-days", "/admin/withdrawals/page": "/admin/withdrawals", "/debug-firestore-issue/page": "/debug-firestore-issue", "/admin/users/page": "/admin/users", "/debug-firestore/page": "/debug-firestore", "/forgot-password/page": "/forgot-password", "/debug-registration/page": "/debug-registration", "/login/page": "/login", "/dashboard/page": "/dashboard", "/debug-registration-simple/page": "/debug-registration-simple", "/debug-dates/page": "/debug-dates", "/profile/page": "/profile", "/plans/page": "/plans", "/page": "/", "/refer/page": "/refer", "/reset-password/page": "/reset-password", "/register/page": "/register", "/registration-diagnostics/page": "/registration-diagnostics", "/test-firebase-connection/page": "/test-firebase-connection", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/test-firebase/page": "/test-firebase", "/support/page": "/support", "/test-functions/page": "/test-functions", "/test-firestore/page": "/test-firestore", "/test-simple-registration/page": "/test-simple-registration", "/test-reg-simple/page": "/test-reg-simple", "/test-videos/page": "/test-videos", "/transactions/page": "/transactions", "/test-registration/page": "/test-registration", "/wallet/page": "/wallet", "/work/page": "/work"}