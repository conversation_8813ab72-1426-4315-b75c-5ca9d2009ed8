{"/_not-found/page": "/_not-found", "/admin/daily-active-days/page": "/admin/daily-active-days", "/admin/fix-active-days/page": "/admin/fix-active-days", "/admin/fix-permissions/page": "/admin/fix-permissions", "/admin/leaves/page": "/admin/leaves", "/admin/fix-plan-expiry/page": "/admin/fix-plan-expiry", "/admin/notifications/page": "/admin/notifications", "/admin/login/page": "/admin/login", "/admin/page": "/admin", "/admin/reset-daily-tracking/page": "/admin/reset-daily-tracking", "/admin/settings/page": "/admin/settings", "/admin/setup/page": "/admin/setup", "/admin/upload-users/page": "/admin/upload-users", "/admin/users/page": "/admin/users", "/admin/withdrawals/page": "/admin/withdrawals", "/dashboard/page": "/dashboard", "/admin/test-blocking/page": "/admin/test-blocking", "/clear-cache/page": "/clear-cache", "/admin/simple-upload/page": "/admin/simple-upload", "/admin/transactions/page": "/admin/transactions", "/debug-dates/page": "/debug-dates", "/debug-active-days/page": "/debug-active-days", "/debug-firestore-issue/page": "/debug-firestore-issue", "/debug-firestore/page": "/debug-firestore", "/page": "/", "/forgot-password/page": "/forgot-password", "/debug-registration-simple/page": "/debug-registration-simple", "/plans/page": "/plans", "/profile/page": "/profile", "/refer/page": "/refer", "/debug-registration/page": "/debug-registration", "/register/page": "/register", "/login/page": "/login", "/registration-diagnostics/page": "/registration-diagnostics", "/reset-password/page": "/reset-password", "/support/page": "/support", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/test-firebase-connection/page": "/test-firebase-connection", "/test-firebase/page": "/test-firebase", "/test-firestore/page": "/test-firestore", "/test-functions/page": "/test-functions", "/test-reg-simple/page": "/test-reg-simple", "/test-registration/page": "/test-registration", "/test-simple-registration/page": "/test-simple-registration", "/test-videos/page": "/test-videos", "/transactions/page": "/transactions", "/wallet/page": "/wallet", "/work/page": "/work"}