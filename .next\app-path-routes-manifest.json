{"/admin/fix-active-days/page": "/admin/fix-active-days", "/_not-found/page": "/_not-found", "/admin/notifications/page": "/admin/notifications", "/admin/fix-plan-expiry/page": "/admin/fix-plan-expiry", "/admin/page": "/admin", "/admin/fix-permissions/page": "/admin/fix-permissions", "/admin/leaves/page": "/admin/leaves", "/admin/settings/page": "/admin/settings", "/admin/simple-upload/page": "/admin/simple-upload", "/admin/login/page": "/admin/login", "/admin/setup/page": "/admin/setup", "/admin/users/page": "/admin/users", "/admin/test-blocking/page": "/admin/test-blocking", "/admin/withdrawals/page": "/admin/withdrawals", "/dashboard/page": "/dashboard", "/debug-active-days/page": "/debug-active-days", "/admin/transactions/page": "/admin/transactions", "/admin/upload-users/page": "/admin/upload-users", "/admin/reset-daily-tracking/page": "/admin/reset-daily-tracking", "/clear-cache/page": "/clear-cache", "/debug-firestore/page": "/debug-firestore", "/debug-firestore-issue/page": "/debug-firestore-issue", "/forgot-password/page": "/forgot-password", "/debug-registration/page": "/debug-registration", "/admin/daily-active-days/page": "/admin/daily-active-days", "/plans/page": "/plans", "/login/page": "/login", "/refer/page": "/refer", "/page": "/", "/profile/page": "/profile", "/debug-dates/page": "/debug-dates", "/debug-registration-simple/page": "/debug-registration-simple", "/reset-password/page": "/reset-password", "/registration-diagnostics/page": "/registration-diagnostics", "/register/page": "/register", "/test-firebase-connection/page": "/test-firebase-connection", "/support/page": "/support", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/test-firestore/page": "/test-firestore", "/test-functions/page": "/test-functions", "/test-firebase/page": "/test-firebase", "/test-reg-simple/page": "/test-reg-simple", "/test-videos/page": "/test-videos", "/test-simple-registration/page": "/test-simple-registration", "/test-registration/page": "/test-registration", "/transactions/page": "/transactions", "/work/page": "/work", "/wallet/page": "/wallet"}