{"/_not-found/page": "/_not-found", "/admin/fix-active-days/page": "/admin/fix-active-days", "/admin/fix-permissions/page": "/admin/fix-permissions", "/admin/daily-active-days/page": "/admin/daily-active-days", "/admin/fix-plan-expiry/page": "/admin/fix-plan-expiry", "/admin/login/page": "/admin/login", "/admin/leaves/page": "/admin/leaves", "/admin/notifications/page": "/admin/notifications", "/admin/settings/page": "/admin/settings", "/admin/page": "/admin", "/admin/reset-daily-tracking/page": "/admin/reset-daily-tracking", "/admin/transactions/page": "/admin/transactions", "/admin/simple-upload/page": "/admin/simple-upload", "/admin/setup/page": "/admin/setup", "/admin/users/page": "/admin/users", "/admin/test-blocking/page": "/admin/test-blocking", "/dashboard/page": "/dashboard", "/admin/upload-users/page": "/admin/upload-users", "/clear-cache/page": "/clear-cache", "/debug-registration-simple/page": "/debug-registration-simple", "/debug-dates/page": "/debug-dates", "/admin/withdrawals/page": "/admin/withdrawals", "/debug-active-days/page": "/debug-active-days", "/debug-firestore-issue/page": "/debug-firestore-issue", "/login/page": "/login", "/plans/page": "/plans", "/page": "/", "/profile/page": "/profile", "/forgot-password/page": "/forgot-password", "/register/page": "/register", "/refer/page": "/refer", "/debug-firestore/page": "/debug-firestore", "/debug-registration/page": "/debug-registration", "/reset-password/page": "/reset-password", "/registration-diagnostics/page": "/registration-diagnostics", "/test-firebase-connection/page": "/test-firebase-connection", "/test-firebase/page": "/test-firebase", "/test-registration/page": "/test-registration", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/support/page": "/support", "/test-firestore/page": "/test-firestore", "/test-functions/page": "/test-functions", "/test-reg-simple/page": "/test-reg-simple", "/test-simple-registration/page": "/test-simple-registration", "/test-videos/page": "/test-videos", "/wallet/page": "/wallet", "/work/page": "/work", "/transactions/page": "/transactions"}