{"/admin/daily-active-days/page": "/admin/daily-active-days", "/admin/login/page": "/admin/login", "/admin/notifications/page": "/admin/notifications", "/admin/fix-permissions/page": "/admin/fix-permissions", "/_not-found/page": "/_not-found", "/admin/page": "/admin", "/admin/leaves/page": "/admin/leaves", "/admin/settings/page": "/admin/settings", "/admin/simple-upload/page": "/admin/simple-upload", "/admin/users/page": "/admin/users", "/admin/test-blocking/page": "/admin/test-blocking", "/admin/reset-daily-tracking/page": "/admin/reset-daily-tracking", "/admin/setup/page": "/admin/setup", "/admin/fix-plan-expiry/page": "/admin/fix-plan-expiry", "/admin/fix-active-days/page": "/admin/fix-active-days", "/dashboard/page": "/dashboard", "/admin/upload-users/page": "/admin/upload-users", "/admin/transactions/page": "/admin/transactions", "/admin/withdrawals/page": "/admin/withdrawals", "/clear-cache/page": "/clear-cache", "/debug-dates/page": "/debug-dates", "/debug-active-days/page": "/debug-active-days", "/debug-firestore-issue/page": "/debug-firestore-issue", "/forgot-password/page": "/forgot-password", "/login/page": "/login", "/page": "/", "/debug-firestore/page": "/debug-firestore", "/debug-registration-simple/page": "/debug-registration-simple", "/plans/page": "/plans", "/profile/page": "/profile", "/debug-registration/page": "/debug-registration", "/register/page": "/register", "/refer/page": "/refer", "/reset-password/page": "/reset-password", "/support/page": "/support", "/registration-diagnostics/page": "/registration-diagnostics", "/test-firebase-connection/page": "/test-firebase-connection", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/test-firebase/page": "/test-firebase", "/test-registration/page": "/test-registration", "/test-firestore/page": "/test-firestore", "/test-functions/page": "/test-functions", "/test-videos/page": "/test-videos", "/test-simple-registration/page": "/test-simple-registration", "/transactions/page": "/transactions", "/wallet/page": "/wallet", "/test-reg-simple/page": "/test-reg-simple", "/work/page": "/work"}