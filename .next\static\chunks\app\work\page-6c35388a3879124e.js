(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4246],{2040:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>b});var o=a(5155),n=a(2115),s=a(6874),r=a.n(s),i=a(6681),l=a(7460),c=a(6572),d=a(3592),u=a(6273),m=a(12),h=a(3631),g=a(9567),x=a(8647),p=a(4752),f=a.n(p);function b(){let{user:e,loading:t}=(0,i.Nu)(),{hasBlockingNotifications:s,isChecking:p,markAllAsRead:b}=(0,l.J)((null==e?void 0:e.uid)||null),{isBlocked:w,leaveStatus:v,checkLeaveStatus:y}=(0,c.l)({userId:(null==e?void 0:e.uid)||null,checkInterval:3e4,enabled:!!e}),[j,N]=(0,n.useState)(null),[S,C]=(0,n.useState)(0),[E,D]=(0,n.useState)(0),[k,T]=(0,n.useState)(1e3),[B,_]=(0,n.useState)(!1),[A,I]=(0,n.useState)(0),[P,V]=(0,n.useState)(!1),[U,O]=(0,n.useState)(!1),[R,L]=(0,n.useState)(!1),[M,W]=(0,n.useState)(0),[G,Q]=(0,n.useState)([]),[F,Y]=(0,n.useState)([]),[H,J]=(0,n.useState)(!1),[Z,q]=(0,n.useState)([]),[z,X]=(0,n.useState)(0),[K,$]=(0,n.useState)(!0),[ee,et]=(0,n.useState)({totalVideos:0,currentBatch:0,totalBatches:0,videosInCurrentBatch:0}),[ea,eo]=(0,n.useState)({videoDuration:300,earningPerBatch:10,plan:"Trial",hasQuickAdvantage:!1,quickAdvantageExpiry:null}),[en,es]=(0,n.useState)(null),[er,ei]=(0,n.useState)(0),[el,ec]=(0,n.useState)(!0),[ed,eu]=(0,n.useState)(!1),[em,eh]=(0,n.useState)(0),eg=(0,n.useRef)(null),ex=(0,n.useRef)(null);(0,n.useEffect)(()=>{e&&ep()},[e]),(0,n.useEffect)(()=>{e&&H&&ef()},[e,H]),(0,n.useEffect)(()=>{console.log("\uD83D\uDD0D Work page leave status check:",{isLeaveBlocked:w,leaveStatus:v,user:null==e?void 0:e.uid}),w&&v.reason&&(console.log("\uD83D\uDEAB Work blocked due to leave:",v.reason),B&&eS(),f().fire({icon:"warning",title:"Work Suspended",text:v.reason,confirmButtonText:"Go to Dashboard",allowOutsideClick:!1,allowEscapeKey:!1}).then(()=>{window.location.href="/dashboard"}))},[w,v,B]);let ep=async()=>{try{console.log("\uD83D\uDD0D Checking work access for user:",e.uid);let t=await (0,d.getUserData)(e.uid);if(!t){console.log("\uD83D\uDEAB Work access blocked - User data not found"),window.location.href="/dashboard";return}let{getLiveActiveDays:o}=await Promise.resolve().then(a.bind(a,3592)),n=await o(e.uid);if("Trial"===t.plan&&n>=3){console.log("\uD83D\uDEAB Work access blocked - Trial plan expired (activeDays >= 3):",n),f().fire({icon:"error",title:"Trial Plan Expired",html:'\n            <div class="text-center">\n              <p class="mb-3">Your trial period has expired.</p>\n              <p class="text-sm text-gray-600">\n                Active Days: '.concat(n,"/2\n              </p>\n            </div>\n          "),confirmButtonText:"Upgrade Plan",showCancelButton:!0,cancelButtonText:"Go to Dashboard"}).then(e=>{e.isConfirmed?window.location.href="/plans":window.location.href="/dashboard"});return}if("Trial"!==t.plan&&n>=31){console.log("\uD83D\uDEAB Work access blocked - Paid plan expired (activeDays >= 31):",n),f().fire({icon:"error",title:"Plan Expired",html:'\n            <div class="text-center">\n              <p class="mb-3">Your plan validity period has expired.</p>\n              <p class="text-sm text-gray-600">\n                Active Days: '.concat(n,"/30\n              </p>\n            </div>\n          "),confirmButtonText:"Upgrade Plan",showCancelButton:!0,cancelButtonText:"Go to Dashboard"}).then(e=>{e.isConfirmed?window.location.href="/plans":window.location.href="/dashboard"});return}let s=await (0,d.isUserPlanExpired)(e.uid);if(console.log("\uD83D\uDCC5 Plan status result:",s),s.expired){console.log("\uD83D\uDEAB Work access blocked - Plan expired:",s.reason),f().fire({icon:"error",title:"Plan Expired",html:'\n            <div class="text-center">\n              <p class="mb-3">'.concat(s.reason,'</p>\n              <p class="text-sm text-gray-600">\n                Active Days: ').concat(s.activeDays||0,"\n              </p>\n            </div>\n          "),confirmButtonText:"Upgrade Plan",showCancelButton:!0,cancelButtonText:"Go to Dashboard"}).then(e=>{e.isConfirmed?window.location.href="/plans":window.location.href="/dashboard"});return}let r=await (0,d.getVideoCountData)(e.uid);if(console.log("\uD83D\uDCCA Video data check:",r),r.todayVideos>=1e3){console.log("\uD83D\uDEAB Work access blocked - Daily session completed"),f().fire({icon:"info",title:"Daily Session Completed",html:'\n            <div class="text-center">\n              <p class="mb-3">You have already completed your daily session of 1000 videos!</p>\n              <p class="text-sm text-gray-600">\n                Videos completed today: '.concat(r.todayVideos,'/1000\n              </p>\n              <p class="text-sm text-green-600 mt-2">\n                Come back tomorrow for your next session.\n              </p>\n            </div>\n          '),confirmButtonText:"Go to Dashboard",allowOutsideClick:!1,allowEscapeKey:!1}).then(()=>{window.location.href="/dashboard"});return}let{debugAdminLeaveStatus:i}=await Promise.resolve().then(a.bind(a,9567));await i();let l=await (0,g.q8)(e.uid);if(console.log("\uD83D\uDCCA Work status result:",l),l.blocked){console.log("\uD83D\uDEAB Work access blocked:",l.reason),f().fire({icon:"warning",title:"Work Not Available",text:l.reason||"Work is currently blocked.",confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"});return}console.log("✅ Work access allowed, proceeding with normal loading"),ef(),eb(),ew(),ey(),ev()}catch(e){console.error("❌ Error checking work access (allowing work to proceed):",e),ef(),eb(),ew(),ey(),ev()}};(0,n.useEffect)(()=>{V(M>=1e3)},[M]),(0,n.useEffect)(()=>{let e=e=>{if(B)return e.preventDefault(),""};return window.addEventListener("beforeunload",e),()=>window.removeEventListener("beforeunload",e)},[B]),(0,n.useEffect)(()=>{let e=()=>{let e=!document.hidden;ec(e),B&&(e?ed&&(eu(!1),I(em),eg.current=setInterval(()=>{I(e=>e<=1?(L(!0),eg.current&&clearInterval(eg.current),0):e-1)},1e3),console.log("✅ Timer resumed - User returned to page/tab")):(eu(!0),eh(A),eg.current&&(clearInterval(eg.current),eg.current=null),console.log("⚠️ Timer paused - User left the page/tab")))},t=()=>{B&&ed&&e()},a=()=>{B&&!ed&&e()};return document.addEventListener("visibilitychange",e),window.addEventListener("focus",t),window.addEventListener("blur",a),()=>{document.removeEventListener("visibilitychange",e),window.removeEventListener("focus",t),window.removeEventListener("blur",a)}},[B,ed,A,em]);let ef=async()=>{try{console.log("\uD83D\uDCCA Loading video data for user:",e.uid);let t=await (0,d.getVideoCountData)(e.uid);console.log("\uD83D\uDCCA Video data loaded:",t),C(t.todayVideos),D(t.totalVideos)}catch(e){console.error("Error loading video data:",e)}},eb=async()=>{try{let t=await (0,d.Q6)(e.uid);eo({videoDuration:t.videoDuration,earningPerBatch:t.earningPerBatch,plan:t.plan,hasQuickAdvantage:t.hasQuickAdvantage||!1,quickAdvantageExpiry:t.quickAdvantageExpiry||null})}catch(e){console.error("Error loading video settings:",e)}},ew=async()=>{try{let t=await (0,d.getUserData)(e.uid);if(es(t),t){try{await (0,d.updateUserActiveDays)(e.uid);let t=await (0,d.getUserData)(e.uid);es(t)}catch(e){console.error("Error updating active days:",e)}let o=await (0,d.isUserPlanExpired)(e.uid),{getLiveActiveDays:n}=await Promise.resolve().then(a.bind(a,3592)),s=await n(e.uid);ei(s),console.log("\uD83D\uDCCA Plan status loaded:",{plan:t.plan,expired:o.expired,activeDays:o.activeDays,reason:o.reason})}}catch(e){console.error("Error loading user data:",e)}},ev=()=>{(0,m.nS)(e.uid);let t=(0,m.g4)(e.uid);t&&(console.log("\uD83D\uDD04 Session data restored after auto-logout"),setTimeout(()=>{f().fire({icon:"info",title:"Session Restored",text:"Your progress has been restored after reconnection.",timer:3e3,showConfirmButton:!1})},1e3));let a=new Date().toDateString(),o="video_session_".concat(e.uid,"_").concat(a),n="watch_times_".concat(e.uid,"_").concat(a),s="daily_watch_times_".concat(e.uid,"_").concat(a),r=(0,m.Mb)(o,e.uid),i=(0,m.Mb)(n,e.uid),l=(0,m.Mb)(s,e.uid);if(r){let e=parseInt(r);W(e),T(Math.max(0,1e3-e)),t&&e>0&&console.log("\uD83D\uDCCA Restored session: ".concat(e,"/1000 videos completed"))}else T(1e3);if(i)try{let e=JSON.parse(i).map(e=>new Date(e));Q(e)}catch(e){console.error("Error parsing saved watch times:",e),Q([])}if(l)try{let e=JSON.parse(l).map(e=>new Date(e));Y(e)}catch(e){console.error("Error parsing saved daily watch times:",e),Y([])}J(!0)},ey=async()=>{try{var t;$(!0);let a=await (0,h.ZB)();q(a);let o=ej(a.length);a.length>0&&(N(a[o]),X(o));let n=(0,h.No)();et(n),console.log("Loaded batch ".concat(n.currentBatch+1,"/").concat(n.totalBatches," with ").concat(a.length," videos")),console.log("Starting with video ".concat(o+1,"/").concat(a.length,": ").concat(null==(t=a[o])?void 0:t.title));let s="video_change_notification_".concat(e.uid);(0,m.Mb)(s,e.uid)||setTimeout(()=>{f().fire({icon:"info",title:"\uD83C\uDFAC Video Variety Feature",html:'\n              <div class="text-left">\n                <p class="mb-2">\uD83D\uDD04 <strong>Refresh to change videos!</strong></p>\n                <p class="mb-2">• Each refresh loads a different video</p>\n                <p class="mb-2">• Click "Change Video" button anytime</p>\n                <p>• Enjoy variety while earning!</p>\n              </div>\n            ',confirmButtonText:"Got it!",timer:8e3,timerProgressBar:!0}),(0,m.CQ)(s,"shown",e.uid)},2e3)}catch(a){console.error("Error initializing videos:",a);let e=[{id:"1",title:"Sample Video 1",url:"https://www.youtube.com/watch?v=dQw4w9WgXcQ",embedUrl:"https://www.youtube.com/embed/dQw4w9WgXcQ",duration:300},{id:"2",title:"Sample Video 2",url:"https://www.youtube.com/watch?v=9bZkp7q19f0",embedUrl:"https://www.youtube.com/embed/9bZkp7q19f0",duration:300},{id:"3",title:"Sample Video 3",url:"https://www.youtube.com/watch?v=L_jWHffIx5E",embedUrl:"https://www.youtube.com/embed/L_jWHffIx5E",duration:300},{id:"4",title:"Sample Video 4",url:"https://www.youtube.com/watch?v=fJ9rUzIMcZQ",embedUrl:"https://www.youtube.com/embed/fJ9rUzIMcZQ",duration:300},{id:"5",title:"Sample Video 5",url:"https://www.youtube.com/watch?v=ZZ5LpwO-An4",embedUrl:"https://www.youtube.com/embed/ZZ5LpwO-An4",duration:300}],t=ej(e.length);q(e),N(e[t]),X(t),f().fire({icon:"warning",title:"Video Loading Issue",text:"Using sample videos. Please check your internet connection.",timer:3e3,showConfirmButton:!1})}finally{$(!1)}},ej=t=>{if(t<=1)return 0;let a=Date.now()+Math.random(),o=new Date().toDateString(),n="video_refresh_".concat(e.uid,"_").concat(o),s=parseInt((0,m.Mb)(n,e.uid)||"0");s+=1,(0,m.CQ)(n,s.toString(),e.uid);let r=Math.floor(s*a%t);return console.log("Refresh #".concat(s," - Selected video index: ").concat(r)),r},eN=async()=>{if(!B){try{let t=await (0,d.getUserData)(e.uid),{getLiveActiveDays:o}=await Promise.resolve().then(a.bind(a,3592)),n=await o(e.uid);if((null==t?void 0:t.plan)==="Trial"&&n>=3||(null==t?void 0:t.plan)!=="Trial"&&n>=31)return void f().fire({icon:"error",title:"Plan Expired",html:'\n            <div class="text-center">\n              <p class="mb-2">Your plan has expired and you cannot watch videos.</p>\n              <p class="text-sm text-gray-600 mb-2">Active Days: '.concat(n,"/").concat((null==t?void 0:t.plan)==="Trial"?"2":"30",'</p>\n              <p class="text-sm text-blue-600">Please upgrade your plan to continue earning.</p>\n            </div>\n          '),confirmButtonText:"Go to Plans",showCancelButton:!0,cancelButtonText:"Go to Dashboard"}).then(e=>{e.isConfirmed?window.location.href="/plans":window.location.href="/dashboard"});let{isUserPlanExpired:s}=await Promise.resolve().then(a.bind(a,3592)),r=await s(e.uid);if(r.expired)return void f().fire({icon:"error",title:"Plan Expired",html:'\n            <div class="text-center">\n              <p class="mb-2">Your plan has expired and you cannot watch videos.</p>\n              <p class="text-sm text-gray-600 mb-2">'.concat(r.reason,'</p>\n              <p class="text-sm text-blue-600">Please upgrade your plan to continue earning.</p>\n            </div>\n          '),confirmButtonText:"Go to Plans",showCancelButton:!0,cancelButtonText:"Go to Dashboard"}).then(e=>{e.isConfirmed?window.location.href="/plans":window.location.href="/dashboard"})}catch(e){console.error("Error checking plan expiry:",e)}if(w)return void f().fire({icon:"warning",title:"Work Not Available",text:v.reason||"Work is currently blocked due to leave.",confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"});if(k<=0||S>=1e3)return void f().fire({icon:"warning",title:"Daily Session Completed",html:'\n          <div class="text-center">\n            <p class="mb-2">You have completed your daily session of 1000 videos!</p>\n            <p class="text-sm text-gray-600">Videos completed today: '.concat(S,'/1000</p>\n            <p class="text-sm text-green-600 mt-2">Come back tomorrow for your next session.</p>\n          </div>\n        '),confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"});if(_(!0),I(ea.videoDuration),L(!1),eu(!1),eh(0),ex.current&&j){let e="".concat(j.embedUrl,"?autoplay=1&mute=0&controls=1&rel=0&modestbranding=1");ex.current.src=e}el?eg.current=setInterval(()=>{I(e=>e<=1?(L(!0),eg.current&&clearInterval(eg.current),0):e-1)},1e3):(eu(!0),eh(ea.videoDuration))}},eS=()=>{_(!1),I(0),L(!1),eu(!1),eh(0),ex.current&&j&&(ex.current.src=j.embedUrl),eg.current&&(clearInterval(eg.current),eg.current=null)},eC=async()=>{if(U)return void console.log("⚠️ Submission already in progress, ignoring click");if(!P||M<1e3){console.log("⚠️ Cannot submit: canSubmit =",P,"localVideoCount =",M),f().fire({icon:"warning",title:"Cannot Submit",text:"You need to complete exactly 1000 videos to submit. Current: ".concat(M,"/1000"),confirmButtonText:"Continue Watching"});return}try{if((await (0,d.getVideoCountData)(e.uid)).todayVideos>=50){console.log("⚠️ User already submitted 50 videos today"),f().fire({icon:"info",title:"Already Submitted",text:"You have already submitted your daily videos. Come back tomorrow!",confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"});return}}catch(e){console.error("Error checking current video data:",e)}try{let t=await (0,d.getUserData)(e.uid),{getLiveActiveDays:o}=await Promise.resolve().then(a.bind(a,3592)),n=await o(e.uid);if((null==t?void 0:t.plan)==="Trial"&&n>=3||(null==t?void 0:t.plan)!=="Trial"&&n>=31)return void f().fire({icon:"error",title:"Plan Expired",html:'\n            <div class="text-center">\n              <p class="mb-2">Your plan has expired and you cannot submit videos.</p>\n              <p class="text-sm text-gray-600 mb-2">Active Days: '.concat(n,"/").concat((null==t?void 0:t.plan)==="Trial"?"2":"30",'</p>\n              <p class="text-sm text-blue-600">Please upgrade your plan to continue earning.</p>\n            </div>\n          '),confirmButtonText:"Go to Plans",showCancelButton:!0,cancelButtonText:"Go to Dashboard"}).then(e=>{e.isConfirmed?window.location.href="/plans":window.location.href="/dashboard"});let{isUserPlanExpired:s}=await Promise.resolve().then(a.bind(a,3592)),r=await s(e.uid);if(r.expired)return void f().fire({icon:"error",title:"Plan Expired",html:'\n            <div class="text-center">\n              <p class="mb-2">Your plan has expired and you cannot submit videos.</p>\n              <p class="text-sm text-gray-600 mb-2">'.concat(r.reason,'</p>\n              <p class="text-sm text-blue-600">Please upgrade your plan to continue earning.</p>\n            </div>\n          '),confirmButtonText:"Go to Plans",showCancelButton:!0,cancelButtonText:"Go to Dashboard"}).then(e=>{e.isConfirmed?window.location.href="/plans":window.location.href="/dashboard"})}catch(e){console.error("Error checking plan expiry:",e)}if(w)return void f().fire({icon:"warning",title:"Submission Not Available",text:v.reason||"Video submission is not available due to leave.",confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"});if(!(await f().fire({title:"Submit Videos & Earn?",html:'\n        <div class="text-left">\n          <p><strong>Videos Completed:</strong> '.concat(M,"/1000</p>\n          <p><strong>Earning Amount:</strong> ₹").concat(ea.earningPerBatch,'</p>\n          <p class="text-sm text-gray-600 mt-2">This action cannot be undone. You can only submit once per day.</p>\n        </div>\n      '),icon:"question",showCancelButton:!0,confirmButtonColor:"#10b981",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Submit & Earn!",cancelButtonText:"Cancel",allowOutsideClick:!1,allowEscapeKey:!1})).isConfirmed)return void console.log("\uD83D\uDCDD User cancelled submission");try{if(O(!0),console.log("\uD83D\uDE80 Starting video submission for 1000 videos..."),1e3!==M)throw Error("Invalid video count: ".concat(M,". Expected exactly 1000 videos."));let t=ea.earningPerBatch;if(console.log("\uD83D\uDCB0 Batch earning amount: ₹".concat(t)),(await (0,d.getVideoCountData)(e.uid)).todayVideos>=50)throw Error("Videos already submitted today. Cannot submit again.");let o=null;try{console.log("\uD83D\uDE80 Submitting batch via optimized function..."),o=await u.x8.submitVideoBatch(e.uid,50),console.log("✅ Batch submitted via optimized function:",o)}catch(s){console.warn("⚠️ Optimized submission failed, using fallback:",s),console.log("\uD83D\uDCCA Submitting batch of 50 videos to database...");let{submitBatchVideos:n}=await Promise.resolve().then(a.bind(a,3592));o=await n(e.uid,50),console.log("✅ Batch submission result:",o),console.log("\uD83D\uDCB0 Adding earnings to wallet..."),await (0,d.updateWalletBalance)(e.uid,t),console.log("\uD83D\uDCDD Adding transaction record..."),await (0,d.addTransaction)(e.uid,{type:"video_earning",amount:t,description:"Batch completion reward - 50 videos watched"})}o&&(C(o.todayVideos||50),D(o.totalVideos||0)),T(0);let n=new Date().toDateString(),s="video_session_".concat(e.uid,"_").concat(n),r="watch_times_".concat(e.uid,"_").concat(n);localStorage.removeItem(s),localStorage.removeItem(r),W(0),Q([]),V(!1),eS(),f().fire({icon:"success",title:"\uD83C\uDF89 Daily Session Completed!",html:'\n          <div class="text-center">\n            <p class="text-lg font-bold text-green-600 mb-2">₹'.concat(t,' Earned!</p>\n            <p class="mb-2">50 videos completed and submitted</p>\n            <p class="text-sm text-gray-600 mb-3">Earnings have been added to your wallet</p>\n            <p class="text-sm text-blue-600 font-semibold">\n              \uD83C\uDF89 Your daily session is complete! Come back tomorrow for your next session.\n            </p>\n          </div>\n        '),confirmButtonText:"Go to Dashboard",timer:6e3,showConfirmButton:!0}).then(()=>{window.location.href="/dashboard"})}catch(t){console.error("Error submitting videos:",t);let e=t instanceof Error?t.message:"Unknown error occurred";f().fire({icon:"error",title:"Submission Failed",html:'\n          <div class="text-left">\n            <p><strong>Error:</strong> '.concat(e,'</p>\n            <p class="text-sm text-gray-600 mt-2">Please try again or contact support if the problem persists.</p>\n            <p class="text-xs text-gray-500 mt-2">If you believe this is an error, please screenshot this message.</p>\n          </div>\n        '),confirmButtonText:"Try Again",allowOutsideClick:!1})}finally{O(!1)}},eE=e=>{let t=Math.floor(e/60);return"".concat(t,":").concat((e%60).toString().padStart(2,"0"))},eD=e=>{let t=Math.floor((new Date().getTime()-e.getTime())/6e4);if(t<1)return"Just now";if(t<60)return"".concat(t," min ago");let a=Math.floor(t/60);if(a<24)return"".concat(a,"h ago");let o=Math.floor(a/24);return"".concat(o,"d ago")};return t||K||p?(0,o.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("div",{className:"spinner mb-4"}),(0,o.jsx)("p",{className:"text-white",children:t?"Loading...":p?"Checking notifications...":"Loading videos..."})]})}):s&&e?(0,o.jsx)(x.A,{userId:e.uid,onAllRead:b}):(0,o.jsxs)("div",{className:"min-h-screen p-4",children:[(0,o.jsxs)("header",{className:"glass-card p-4 mb-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,o.jsxs)(r(),{href:"/dashboard",className:"glass-button px-4 py-2 text-white",children:[(0,o.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]}),(0,o.jsx)("h1",{className:"text-xl font-bold text-white",children:"Watch Videos & Earn"}),(0,o.jsxs)("div",{className:"text-white text-right",children:[(0,o.jsxs)("p",{className:"text-sm",children:["Plan: ",ea.plan]}),(0,o.jsxs)("p",{className:"text-sm",children:["₹",ea.earningPerBatch,"/batch (50 videos)"]})]})]}),(0,o.jsx)("div",{className:"bg-blue-500/20 border border-blue-400/30 rounded-lg p-3 mb-4",children:(0,o.jsxs)("div",{className:"flex items-center justify-center text-center",children:[(0,o.jsx)("i",{className:"fas fa-sync-alt text-blue-400 mr-2"}),(0,o.jsx)("span",{className:"text-white/90 text-sm",children:'Refresh page or click "Change Video" for different content'})]})}),(0,o.jsxs)("div",{className:"grid grid-cols-4 gap-2 text-center",children:[(0,o.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,o.jsx)("p",{className:"text-lg font-bold text-blue-400",children:S}),(0,o.jsx)("p",{className:"text-white/80 text-xs",children:"Today's Videos"})]}),(0,o.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,o.jsx)("p",{className:"text-lg font-bold text-green-400",children:E}),(0,o.jsx)("p",{className:"text-white/80 text-xs",children:"Total Videos"})]}),(0,o.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,o.jsx)("p",{className:"text-lg font-bold text-purple-400",children:k}),(0,o.jsx)("p",{className:"text-white/80 text-xs",children:"Videos Left"})]}),(0,o.jsxs)("div",{className:"bg-white/10 rounded-lg p-3",children:[(0,o.jsxs)("p",{className:"text-lg font-bold text-orange-400",children:[er,"/","Trial"===ea.plan?"2":"30"]}),(0,o.jsx)("p",{className:"text-white/80 text-xs",children:"Active Days"})]})]})]}),(0,o.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,o.jsxs)("h2",{className:"text-xl font-bold text-white",children:[(0,o.jsx)("i",{className:"fas fa-play-circle mr-2"}),"Watch Video & Earn"]}),(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[ea.hasQuickAdvantage&&(0,o.jsxs)("div",{className:"bg-green-500/20 border border-green-400/30 rounded-lg px-3 py-1",children:[(0,o.jsxs)("div",{className:"flex items-center text-green-300 text-sm",children:[(0,o.jsx)("i",{className:"fas fa-bolt mr-1"}),(0,o.jsx)("span",{className:"font-medium",children:"Quick Advantage Active"})]}),ea.quickAdvantageExpiry&&(0,o.jsxs)("div",{className:"text-xs text-green-400 mt-1",children:["Until: ",new Date(ea.quickAdvantageExpiry).toLocaleDateString()]})]}),(0,o.jsxs)("button",{onClick:()=>window.location.reload(),className:"glass-button px-3 py-1 text-white text-sm",title:"Refresh to change video",children:[(0,o.jsx)("i",{className:"fas fa-sync-alt mr-1"}),"Change Video"]})]})]}),j&&(0,o.jsxs)("div",{className:"aspect-video mb-4 video-container ".concat(B?"watching":""),children:[(0,o.jsx)("iframe",{ref:ex,src:B?"".concat(j.embedUrl,"?autoplay=1&mute=0&controls=1&rel=0&modestbranding=1&disablekb=1"):j.embedUrl,title:j.title,className:"w-full h-full rounded-lg border-0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0}),B&&(0,o.jsx)("div",{className:"video-protection-overlay rounded-lg"}),!B&&(0,o.jsx)("div",{className:"absolute inset-0 bg-black/30 backdrop-blur-sm rounded-lg flex items-center justify-center",children:(0,o.jsx)("div",{className:"text-center text-white",children:(0,o.jsx)("i",{className:"fas fa-play-circle text-6xl opacity-60 text-youtube-red"})})})]}),(0,o.jsx)("div",{className:"text-center",children:B?(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsx)("div",{className:"text-3xl font-bold text-white",children:eE(A)}),ed&&(0,o.jsx)("div",{className:"bg-red-500/20 border border-red-400/30 rounded-lg p-3 mb-4",children:(0,o.jsxs)("div",{className:"flex items-center justify-center text-center",children:[(0,o.jsx)("i",{className:"fas fa-pause text-red-400 mr-2"}),(0,o.jsx)("span",{className:"text-red-300 text-sm font-medium",children:"Timer Paused - Please stay on this page to continue watching"})]})}),(0,o.jsx)("div",{className:"bg-white/20 rounded-full h-3 max-w-md mx-auto",children:(0,o.jsx)("div",{className:"h-3 rounded-full transition-all duration-1000 ".concat(ed?"bg-red-500":"bg-youtube-red"),style:{width:"".concat((ea.videoDuration-A)/ea.videoDuration*100,"%")}})}),(0,o.jsxs)("div",{className:"space-x-4",children:[(0,o.jsxs)("button",{onClick:eS,className:"btn-secondary",children:[(0,o.jsx)("i",{className:"fas fa-stop mr-2"}),"Stop Watching"]}),R&&(0,o.jsx)("button",{onClick:()=>{if(!R||U)return;if(w){eS(),f().fire({icon:"warning",title:"Work Suspended",text:v.reason||"Work has been suspended due to leave.",confirmButtonText:"Go to Dashboard"}).then(()=>{window.location.href="/dashboard"});return}let t=M+1,a=new Date,o=[...G,a],n=[...F,a];W(t),Q(o),Y(n),T(Math.max(0,50-t));let s=new Date().toDateString(),r="video_session_".concat(e.uid,"_").concat(s),i="watch_times_".concat(e.uid,"_").concat(s),l="daily_watch_times_".concat(e.uid,"_").concat(s);(0,m.CQ)(r,t.toString(),e.uid),(0,m.CQ)(i,JSON.stringify(o.map(e=>e.toISOString())),e.uid),(0,m.CQ)(l,JSON.stringify(n.map(e=>e.toISOString())),e.uid);let c=z+1;if(c>=Z.length)try{let e=(0,h.CA)();q(e);let a=Math.floor(Math.random()*e.length);X(a),N(e[a]);let o=(0,h.No)();et(o),f().fire({icon:"info",title:"New Video Batch Loaded",text:"Video ".concat(t,"/50 completed. Batch ").concat(o.currentBatch+1,"/").concat(o.totalBatches," loaded."),timer:2e3,showConfirmButton:!1})}catch(t){console.error("Error loading next batch:",t);let e=Math.floor(Math.random()*Z.length);X(e),N(Z[e])}else{if(.3>Math.random()&&Z.length>3){let e=Z.map((e,t)=>t).filter(e=>e!==z);c=e[Math.floor(Math.random()*e.length)],console.log("Randomized next video: ".concat(c," (was going to ").concat(z+1,")"))}X(c),N(Z[c])}eS(),t<50?f().fire({icon:"success",title:"Video Completed!",text:"Progress: ".concat(t,"/50 videos watched. ").concat(50-t," more to go!"),timer:2e3,showConfirmButton:!1}):f().fire({icon:"success",title:"\uD83C\uDF89 All Videos Completed!",text:'You have watched all 50 videos! Click "Submit & Earn" to get your rewards.',timer:3e3,showConfirmButton:!1})},disabled:U||!R,className:"".concat(U?"btn-disabled cursor-not-allowed opacity-50":"btn-primary"),children:U?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Processing..."]}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("i",{className:"fas fa-arrow-right mr-2"}),"Next Video"]})})]})]}):(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsx)("button",{onClick:eN,disabled:M>=1e3||B||U,className:"text-lg px-8 py-4 ".concat(M>=1e3||B||U?"btn-disabled cursor-not-allowed opacity-50":ea.hasQuickAdvantage?"btn-success bg-green-500 hover:bg-green-600":"btn-primary"),children:B?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Starting Video..."]}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("i",{className:"mr-2 ".concat(ea.hasQuickAdvantage?"fas fa-bolt":"fas fa-play")}),ea.hasQuickAdvantage?"Quick Watch":"Start Watching"," (",eE(ea.videoDuration),")"]})}),P&&M>=1e3&&(0,o.jsx)("button",{onClick:eC,disabled:U||1e3!==M||S>=1e3,className:"text-lg px-8 py-4 transition-all duration-200 ".concat(U||1e3!==M||S>=1e3?"btn-disabled cursor-not-allowed opacity-50 bg-gray-500":"btn-success bg-green-500 hover:bg-green-600 active:bg-green-700 transform hover:scale-105 active:scale-95"),style:{pointerEvents:U?"none":"auto"},children:U?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Submitting All Videos..."]}):1e3!==M?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("i",{className:"fas fa-exclamation-triangle mr-2"}),"Need ",1e3-M," More Videos"]}):S>=1e3?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("i",{className:"fas fa-check-circle mr-2"}),"Already Submitted Today"]}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("i",{className:"fas fa-trophy mr-2"}),"Submit & Earn ₹",ea.earningPerBatch]})}),M>0&&M<1e3&&(0,o.jsx)("div",{className:"mt-4 bg-blue-500/20 border border-blue-400/30 rounded-lg p-3",children:(0,o.jsxs)("div",{className:"flex items-center justify-center text-center",children:[(0,o.jsx)("i",{className:"fas fa-info-circle text-blue-400 mr-2"}),(0,o.jsxs)("span",{className:"text-blue-300 text-sm",children:["Progress: ",M,"/1000 videos completed (",1e3-M," remaining)"]})]})})]})})]}),F.length>0&&(0,o.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,o.jsxs)("h2",{className:"text-xl font-bold text-white",children:[(0,o.jsx)("i",{className:"fas fa-clock mr-2"}),"Today's Watch History"]}),(0,o.jsxs)("div",{className:"text-white/70 text-sm",children:["Total: ",F.length," videos watched"]})]}),(0,o.jsx)("div",{className:"max-h-64 overflow-y-auto",children:(0,o.jsx)("div",{className:"grid gap-2",children:F.map((e,t)=>(0,o.jsxs)("div",{className:"bg-white/10 rounded-lg p-3 flex items-center justify-between",children:[(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("div",{className:"bg-youtube-red/20 rounded-full p-2 mr-3",children:(0,o.jsx)("i",{className:"fas fa-play text-youtube-red text-sm"})}),(0,o.jsxs)("div",{children:[(0,o.jsxs)("p",{className:"text-white font-medium",children:["Video #",t+1]}),(0,o.jsx)("p",{className:"text-white/70 text-sm",children:e.toLocaleDateString("en-IN",{weekday:"short",year:"numeric",month:"short",day:"numeric"})})]})]}),(0,o.jsxs)("div",{className:"text-right",children:[(0,o.jsx)("p",{className:"text-white font-medium",children:e.toLocaleTimeString("en-IN",{hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!0})}),(0,o.jsx)("p",{className:"text-white/70 text-xs",children:eD(e)})]})]},t))})}),F.length>=50&&(0,o.jsx)("div",{className:"mt-4 bg-green-500/20 border border-green-400/30 rounded-lg p-3",children:(0,o.jsxs)("div",{className:"flex items-center justify-center text-center",children:[(0,o.jsx)("i",{className:"fas fa-trophy text-green-400 mr-2"}),(0,o.jsx)("span",{className:"text-green-300 text-sm font-medium",children:"Daily target completed! Great job! \uD83C\uDF89"})]})})]})]})}},3631:(e,t,a)=>{"use strict";a.d(t,{CA:()=>c,No:()=>d,ZB:()=>h,iD:()=>u,tx:()=>m});let o={CURRENT_BATCH:"mytube_current_batch",BATCH_PREFIX:"mytube_batch_",VIDEO_INDEX:"mytube_video_index",TOTAL_VIDEOS:"mytube_total_videos",LAST_PROCESSED:"mytube_last_processed"};function n(e){for(let t of[/(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,/youtube\.com\/v\/([^&\n?#]+)/]){let a=e.match(t);if(a)return a[1]}return null}function s(e){let t=n(e);return t?"https://www.youtube.com/embed/".concat(t):e}function r(e,t){return"Video ".concat(t+1)}function i(e){let t=function(e){try{let t=localStorage.getItem("".concat(o.BATCH_PREFIX).concat(e));if(!t)return null;let a=JSON.parse(t);if(Date.now()-a.lastUpdated>864e5)return localStorage.removeItem("".concat(o.BATCH_PREFIX).concat(e)),null;return a}catch(t){return console.error("Error loading batch ".concat(e,":"),t),null}}(e);return t?t.videos:[]}function l(){return i(parseInt(localStorage.getItem(o.CURRENT_BATCH)||"0"))}function c(){let e=(parseInt(localStorage.getItem(o.CURRENT_BATCH)||"0")+1)%Math.ceil(parseInt(localStorage.getItem(o.TOTAL_VIDEOS)||"0")/100);return localStorage.setItem(o.CURRENT_BATCH,e.toString()),i(e)}function d(){let e=parseInt(localStorage.getItem(o.TOTAL_VIDEOS)||"0"),t=parseInt(localStorage.getItem(o.CURRENT_BATCH)||"0"),a=Math.ceil(e/100),n=i(t);return{totalVideos:e,currentBatch:t,totalBatches:a,videosInCurrentBatch:n.length}}function u(){Object.keys(localStorage).forEach(e=>{(e.startsWith(o.BATCH_PREFIX)||Object.values(o).includes(e))&&localStorage.removeItem(e)}),console.log("Cleared all video storage")}async function m(){try{let e=await fetch("/Mytube.json");if(!e.ok)throw Error("Failed to load videos: ".concat(e.statusText));let t=await e.json();console.log("Raw video data loaded:",Object.keys(t).length,"entries");let a=[];return Array.isArray(t)?t.forEach((e,t)=>{Object.entries(e).forEach(e=>{let[t,o]=e,i=n(o);i&&a.push({id:"video_".concat(a.length,"_").concat(i),title:r(o,a.length),url:o,embedUrl:s(o),duration:300,category:"General",batchIndex:Math.floor(a.length/100)})})}):Object.entries(t).forEach((e,t)=>{let[o,i]=e,l=n(i);l&&a.push({id:"video_".concat(a.length,"_").concat(l),title:r(i,a.length),url:i,embedUrl:s(i),duration:300,category:"General",batchIndex:Math.floor(a.length/100)})}),a}catch(e){throw console.error("Error loading videos from file:",e),e}}async function h(){try{if(!function(){let e=localStorage.getItem(o.LAST_PROCESSED);return!e||Date.now()-parseInt(e)>864e5}())return console.log("Using cached video data..."),l();{console.log("Loading fresh video data...");let e=await m();return!function(e){let t=Math.ceil(e.length/100);for(let n=0;n<t;n++){let t=100*n,s=Math.min(t+100,e.length),r=e.slice(t,s);var a=n;try{let e={batchNumber:a,videos:r,totalVideos:r.length,lastUpdated:Date.now()};localStorage.setItem("".concat(o.BATCH_PREFIX).concat(a),JSON.stringify(e))}catch(e){console.error("Error saving batch ".concat(a,":"),e)}}localStorage.setItem(o.TOTAL_VIDEOS,e.length.toString()),localStorage.setItem(o.CURRENT_BATCH,"0"),localStorage.setItem(o.LAST_PROCESSED,Date.now().toString()),console.log("Saved ".concat(e.length," videos in ").concat(t," batches"))}(e),l()}}catch(t){console.error("Error initializing video system:",t);let e=l();if(e.length>0)return console.log("Using cached videos as fallback"),e;throw t}}},8305:(e,t,a)=>{Promise.resolve().then(a.bind(a,2040))}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,8818,6874,3592,6681,6119,8441,1684,7358],()=>t(8305)),_N_E=e.O()}]);