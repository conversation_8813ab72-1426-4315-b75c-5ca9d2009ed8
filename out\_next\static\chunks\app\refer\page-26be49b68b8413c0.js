(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4363],{2813:(e,s,t)=>{Promise.resolve().then(t.bind(t,4665))},4665:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var a=t(5155),r=t(2115),l=t(6874),i=t.n(l),n=t(6681),c=t(3592),d=t(4752),o=t.n(d);function x(){let{user:e,loading:s}=(0,n.Nu)(),[t,l]=(0,r.useState)(null),[d,x]=(0,r.useState)([]),[m,h]=(0,r.useState)(!0),[f,u]=(0,r.useState)(0);(0,r.useEffect)(()=>{e&&j()},[e]);let j=async()=>{try{h(!0);let s=await (0,c.getUserData)(e.uid);if(l(s),null==s?void 0:s.referralCode){let e=await (0,c.pl)(s.referralCode);x(e);let t=e.reduce((e,s)=>e+N(s.plan),0);u(t)}}catch(e){console.error("Error loading referral data:",e),o().fire({icon:"error",title:"Error",text:"Failed to load referral data. Please try again."})}finally{h(!1)}},p=e=>{switch(e){case"Trial":default:return"bg-gray-500";case"Starter":return"bg-blue-500";case"Basic":return"bg-green-500";case"Premium":return"bg-purple-500";case"Gold":return"bg-yellow-500";case"Platinum":return"bg-indigo-500";case"Diamond":return"bg-pink-500"}},N=e=>({Trial:0,499:50,1499:150,2999:300,3999:400,5999:700,9999:1200,Starter:50,Basic:150,Premium:300,Gold:400,Platinum:700,Diamond:1200})[e]||0;return s||m?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)("div",{className:"spinner"})}):(0,a.jsxs)("div",{className:"min-h-screen p-4",children:[(0,a.jsx)("header",{className:"glass-card p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(i(),{href:"/dashboard",className:"glass-button px-4 py-2 text-white",children:[(0,a.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]}),(0,a.jsx)("h1",{className:"text-xl font-bold text-white",children:"Refer & Earn"}),(0,a.jsxs)("button",{onClick:j,className:"glass-button px-4 py-2 text-white",children:[(0,a.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})}),(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-code mr-2"}),"Your Referral Code"]}),(0,a.jsx)("div",{className:"bg-white/10 p-4 rounded-lg mb-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-2xl font-mono font-bold text-white",children:(null==t?void 0:t.referralCode)||"Loading..."}),(0,a.jsxs)("button",{onClick:()=>{(null==t?void 0:t.referralCode)&&(navigator.clipboard.writeText(t.referralCode),o().fire({icon:"success",title:"Copied!",text:"Referral code copied to clipboard",timer:1500,showConfirmButton:!1}))},className:"glass-button px-4 py-2 text-white",children:[(0,a.jsx)("i",{className:"fas fa-copy mr-2"}),"Copy"]})]})}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,a.jsxs)("button",{onClick:()=>{if(null==t?void 0:t.referralCode){let e="".concat(window.location.origin,"/register?ref=").concat(t.referralCode);navigator.clipboard.writeText(e),o().fire({icon:"success",title:"Link Copied!",text:"Referral link copied to clipboard",timer:2e3,showConfirmButton:!1})}},className:"btn-primary",children:[(0,a.jsx)("i",{className:"fas fa-link mr-2"}),"Copy Referral Link"]}),(0,a.jsxs)("button",{onClick:()=>{if(null==t?void 0:t.referralCode){let e="".concat(window.location.origin,"/register?ref=").concat(t.referralCode),s="\uD83C\uDF89 Join MyTube and start earning money by watching videos!\n\n\uD83D\uDCB0 Earn up to ₹30,000 per month\n\uD83C\uDFAC Watch videos and get paid\n⚡ Quick and easy registration\n\nUse my referral code: ".concat(t.referralCode,"\n\nJoin now: ").concat(e);navigator.share?navigator.share({title:"Join MyTube and Start Earning",text:s,url:e}):(navigator.clipboard.writeText(s),o().fire({icon:"success",title:"Message Copied!",text:"Referral message copied to clipboard. Share it with your friends!",timer:2e3,showConfirmButton:!1}))}},className:"btn-secondary",children:[(0,a.jsx)("i",{className:"fas fa-share mr-2"}),"Share with Friends"]})]})]}),(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-list mr-2"}),"Your Referrals (",d.length,")"]}),0===d.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("i",{className:"fas fa-users text-white/30 text-4xl mb-4"}),(0,a.jsx)("p",{className:"text-white/60 mb-4",children:"No referrals yet"}),(0,a.jsx)("p",{className:"text-white/40 text-sm",children:"Start sharing your referral code to earn bonuses!"})]}):(0,a.jsx)("div",{className:"space-y-3",children:d.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-white/10 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mr-3",children:(0,a.jsx)("span",{className:"text-white font-bold",children:e.name.charAt(0).toUpperCase()})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white font-medium",children:e.name}),(0,a.jsx)("p",{className:"text-white/60 text-sm",children:e.email}),(0,a.jsxs)("p",{className:"text-white/60 text-sm",children:["Joined: ",e.joinedDate.toLocaleDateString()]})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("span",{className:"px-3 py-1 rounded-full text-white text-sm ".concat(p(e.plan)),children:e.plan}),(0,a.jsxs)("p",{className:"text-green-400 font-bold mt-1",children:["+₹",N(e.plan)]})]})]},e.id))})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-3 gap-6 mb-6",children:[(0,a.jsxs)("div",{className:"glass-card p-6 text-center",children:[(0,a.jsx)("i",{className:"fas fa-users text-4xl text-blue-400 mb-4"}),(0,a.jsx)("h3",{className:"text-2xl font-bold text-white",children:d.length}),(0,a.jsx)("p",{className:"text-white/80",children:"Total Referrals"})]}),(0,a.jsxs)("div",{className:"glass-card p-6 text-center",children:[(0,a.jsx)("i",{className:"fas fa-rupee-sign text-4xl text-green-400 mb-4"}),(0,a.jsxs)("h3",{className:"text-2xl font-bold text-white",children:["₹",f]}),(0,a.jsx)("p",{className:"text-white/80",children:"Total Earnings"})]}),(0,a.jsxs)("div",{className:"glass-card p-6 text-center",children:[(0,a.jsx)("i",{className:"fas fa-gift text-4xl text-purple-400 mb-4"}),(0,a.jsx)("h3",{className:"text-2xl font-bold text-white",children:d.filter(e=>"Trial"!==e.plan).length}),(0,a.jsx)("p",{className:"text-white/80",children:"Paid Referrals"})]})]}),(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-info-circle mr-2"}),"How Referral Works"]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3 mt-1",children:(0,a.jsx)("span",{className:"text-white font-bold",children:"1"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-white font-semibold",children:"Share Your Code"}),(0,a.jsx)("p",{className:"text-white/80 text-sm",children:"Share your referral code or link with friends"})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3 mt-1",children:(0,a.jsx)("span",{className:"text-white font-bold",children:"2"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-white font-semibold",children:"Friend Joins"}),(0,a.jsx)("p",{className:"text-white/80 text-sm",children:"Your friend registers using your referral code"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center mr-3 mt-1",children:(0,a.jsx)("span",{className:"text-white font-bold",children:"3"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-white font-semibold",children:"Friend Upgrades"}),(0,a.jsx)("p",{className:"text-white/80 text-sm",children:"When they purchase a plan, you earn bonus"})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center mr-3 mt-1",children:(0,a.jsx)("span",{className:"text-white font-bold",children:"4"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-white font-semibold",children:"You Earn + 50 Videos"}),(0,a.jsx)("p",{className:"text-white/80 text-sm",children:"₹50-₹1200 bonus + 50 lifetime videos based on plan"}),(0,a.jsxs)("div",{className:"mt-2 text-xs text-white/60",children:[(0,a.jsx)("div",{children:"₹499 Plan: ₹50 • ₹2999 Plan: ₹300"}),(0,a.jsx)("div",{children:"₹3999 Plan: ₹400 • ₹5999 Plan: ₹700 • ₹9999 Plan: ₹1200"})]})]})]})]})]})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[2992,7416,8320,8818,6874,3592,6681,8441,1684,7358],()=>s(2813)),_N_E=e.O()}]);