(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4831],{4165:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>o});var s=a(5155),n=a(2115),r=a(3004),i=a(5317),l=a(6104),c=a(3592);function o(){let[e,t]=(0,n.useState)(""),[a,o]=(0,n.useState)(!1),d=async()=>{t(""),o(!0);try{t(e=>e+"Testing referral code generation...\n");let e=await (0,c.pu)();t(t=>t+"✓ Referral code generated: ".concat(e,"\n"));for(let e=0;e<3;e++){let a=await (0,c.pu)();t(t=>t+"✓ Code ".concat(e+2,": ").concat(a,"\n"))}}catch(e){t(t=>t+"✗ Error: ".concat(e.message,"\n")),console.error("Test error:",e)}finally{o(!1)}},u=async()=>{t(""),o(!0);try{t(e=>e+"Testing Firestore write...\n");let e={name:"Test User",email:"<EMAIL>",mobile:"9876543210",referralCode:"TEST001",referredBy:"",plan:"Trial",planExpiry:null,activeDays:2,joinedDate:i.Dc.now(),wallet:0,totalVideos:0,todayVideos:0,lastVideoDate:null,status:"active"},a="test_".concat(Date.now()),s=(0,i.H9)(l.db,c.COLLECTIONS.users,a);t(e=>e+"Creating document: ".concat(s.path,"\n")),await (0,i.BN)(s,e),t(e=>e+"✓ Document created successfully\n");let n=await (0,i.x7)(s);n.exists()?(t(e=>e+"✓ Document verified successfully\n"),t(e=>e+"Data: ".concat(JSON.stringify(n.data(),null,2),"\n"))):t(e=>e+"✗ Document not found after creation\n")}catch(e){t(t=>t+"✗ Error: ".concat(e.message,"\n")),console.error("Test error:",e)}finally{o(!1)}},m=async()=>{t(""),o(!0);try{t(e=>e+"Testing full registration flow...\n");let e="test_".concat(Date.now(),"@example.com");t(t=>t+"Creating auth user: ".concat(e,"\n"));let a=(await (0,r.eJ)(l.j2,e,"test123456")).user;t(e=>e+"✓ Auth user created: ".concat(a.uid,"\n")),t(e=>e+"Generating referral code...\n");let s=await (0,c.pu)();t(e=>e+"✓ Referral code: ".concat(s,"\n"));let n={[c.FIELD_NAMES.name]:"Test Registration User",[c.FIELD_NAMES.email]:e,[c.FIELD_NAMES.mobile]:"9876543210",[c.FIELD_NAMES.referralCode]:s,[c.FIELD_NAMES.referredBy]:"",[c.FIELD_NAMES.plan]:"Trial",[c.FIELD_NAMES.planExpiry]:null,[c.FIELD_NAMES.activeDays]:1,[c.FIELD_NAMES.joinedDate]:i.Dc.now(),[c.FIELD_NAMES.wallet]:0,[c.FIELD_NAMES.totalVideos]:0,[c.FIELD_NAMES.todayVideos]:0,[c.FIELD_NAMES.lastVideoDate]:null,status:"active"};t(e=>e+"Creating Firestore document...\n");let o=(0,i.H9)(l.db,c.COLLECTIONS.users,a.uid);await (0,i.BN)(o,n),t(e=>e+"✓ Firestore document created\n"),(await (0,i.x7)(o)).exists()?(t(e=>e+"✓ Document verified successfully\n"),t(e=>e+"✓ Full registration test completed successfully!\n")):t(e=>e+"✗ Document verification failed\n"),t(e=>e+"Cleaning up test user...\n"),await a.delete(),t(e=>e+"✓ Test user deleted\n")}catch(e){t(t=>t+"✗ Error: ".concat(e.message,"\n")),console.error("Test error:",e)}finally{o(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-pink-900 p-4",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-white mb-4",children:"Registration Debug Tests"}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-4 mb-6",children:[(0,s.jsx)("button",{onClick:d,disabled:a,className:"btn-primary",children:"Test Referral Code Generation"}),(0,s.jsx)("button",{onClick:u,disabled:a,className:"btn-primary",children:"Test Firestore Write"}),(0,s.jsx)("button",{onClick:m,disabled:a,className:"btn-primary",children:"Test Full Registration"})]}),a&&(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[(0,s.jsx)("div",{className:"spinner mr-2"}),(0,s.jsx)("span",{className:"text-white",children:"Running test..."})]}),(0,s.jsx)("div",{className:"bg-black/50 rounded-lg p-4 min-h-[200px]",children:(0,s.jsx)("pre",{className:"text-green-400 text-sm whitespace-pre-wrap font-mono",children:e||"Click a test button to start..."})})]}),(0,s.jsxs)("div",{className:"glass-card p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-white mb-4",children:"Debug Information"}),(0,s.jsxs)("div",{className:"text-white/80 space-y-2",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Firebase Project:"})," ","mytube-india"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Auth Domain:"})," ","mytube-india.firebaseapp.com"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Collections.users:"})," ",c.COLLECTIONS.users]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Field Names:"})," ",JSON.stringify(c.FIELD_NAMES,null,2)]})]})]})]})})}},6104:(e,t,a)=>{"use strict";a.d(t,{Cn:()=>u,db:()=>d,j2:()=>o});var s=a(3915),n=a(3004),r=a(5317),i=a(858),l=a(2144);let c=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),o=(0,n.xI)(c),d=(0,r.aU)(c);(0,i.c7)(c);let u=(0,l.Uz)(c,"us-central1")},7797:(e,t,a)=>{Promise.resolve().then(a.bind(a,4165))}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8818,3592,8441,1684,7358],()=>t(7797)),_N_E=e.O()}]);