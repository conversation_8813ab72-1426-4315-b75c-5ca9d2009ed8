{"/admin/fix-active-days/page": "app/admin/fix-active-days/page.js", "/_not-found/page": "app/_not-found/page.js", "/admin/notifications/page": "app/admin/notifications/page.js", "/admin/fix-plan-expiry/page": "app/admin/fix-plan-expiry/page.js", "/admin/page": "app/admin/page.js", "/admin/fix-permissions/page": "app/admin/fix-permissions/page.js", "/admin/leaves/page": "app/admin/leaves/page.js", "/admin/settings/page": "app/admin/settings/page.js", "/admin/simple-upload/page": "app/admin/simple-upload/page.js", "/admin/login/page": "app/admin/login/page.js", "/admin/setup/page": "app/admin/setup/page.js", "/admin/users/page": "app/admin/users/page.js", "/admin/test-blocking/page": "app/admin/test-blocking/page.js", "/admin/withdrawals/page": "app/admin/withdrawals/page.js", "/dashboard/page": "app/dashboard/page.js", "/debug-active-days/page": "app/debug-active-days/page.js", "/admin/transactions/page": "app/admin/transactions/page.js", "/admin/upload-users/page": "app/admin/upload-users/page.js", "/admin/reset-daily-tracking/page": "app/admin/reset-daily-tracking/page.js", "/clear-cache/page": "app/clear-cache/page.js", "/debug-firestore/page": "app/debug-firestore/page.js", "/debug-firestore-issue/page": "app/debug-firestore-issue/page.js", "/forgot-password/page": "app/forgot-password/page.js", "/debug-registration/page": "app/debug-registration/page.js", "/admin/daily-active-days/page": "app/admin/daily-active-days/page.js", "/plans/page": "app/plans/page.js", "/login/page": "app/login/page.js", "/refer/page": "app/refer/page.js", "/page": "app/page.js", "/profile/page": "app/profile/page.js", "/debug-dates/page": "app/debug-dates/page.js", "/debug-registration-simple/page": "app/debug-registration-simple/page.js", "/reset-password/page": "app/reset-password/page.js", "/registration-diagnostics/page": "app/registration-diagnostics/page.js", "/register/page": "app/register/page.js", "/test-firebase-connection/page": "app/test-firebase-connection/page.js", "/support/page": "app/support/page.js", "/test-firebase-connectivity/page": "app/test-firebase-connectivity/page.js", "/test-firestore/page": "app/test-firestore/page.js", "/test-functions/page": "app/test-functions/page.js", "/test-firebase/page": "app/test-firebase/page.js", "/test-reg-simple/page": "app/test-reg-simple/page.js", "/test-videos/page": "app/test-videos/page.js", "/test-simple-registration/page": "app/test-simple-registration/page.js", "/test-registration/page": "app/test-registration/page.js", "/transactions/page": "app/transactions/page.js", "/work/page": "app/work/page.js", "/wallet/page": "app/wallet/page.js"}