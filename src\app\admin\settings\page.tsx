'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRequireAdmin } from '@/hooks/useAuth'
import Swal from 'sweetalert2'

interface SystemSettings {
  appName: string
  appVersion: string
  maintenanceMode: boolean
  registrationEnabled: boolean
  minWithdrawalAmount: number
  maxWithdrawalAmount: number
  withdrawalProcessingTime: string
  supportEmail: string
  supportPhone: string
  defaultVideoDuration: number
  maxDailyVideos: number
  referralBonus: number
  videoEarningRates: {
    trial: number
    starter: number
    basic: number
    premium: number
    gold: number
    platinum: number
    diamond: number
  }
}

export default function AdminSettingsPage() {
  const { user, loading, isAdmin } = useRequireAdmin()
  const [settings, setSettings] = useState<SystemSettings>({
    appName: 'MyTube',
    appVersion: '1.0.0',
    maintenanceMode: false,
    registrationEnabled: true,
    minWithdrawalAmount: 50,
    maxWithdrawalAmount: 50000,
    withdrawalProcessingTime: '24-48 hours',
    supportEmail: '<EMAIL>',
    supportPhone: '+917676636990',
    defaultVideoDuration: 300,
    maxDailyVideos: 1000,
    referralBonus: 50,
    videoEarningRates: {
      trial: 10,
      starter: 25,
      basic: 75,
      premium: 150,
      gold: 200,
      platinum: 250,
      diamond: 400
    }
  })
  const [dataLoading, setDataLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)

  useEffect(() => {
    if (isAdmin) {
      loadSettings()
    }
  }, [isAdmin])

  const loadSettings = async () => {
    try {
      setDataLoading(true)
      // Load settings from database
      // For now, using default values
      setDataLoading(false)
    } catch (error) {
      console.error('Error loading settings:', error)
      setDataLoading(false)
    }
  }

  const handleSaveSettings = async () => {
    try {
      setIsSaving(true)
      
      // Validate settings
      if (settings.minWithdrawalAmount >= settings.maxWithdrawalAmount) {
        throw new Error('Minimum withdrawal amount must be less than maximum withdrawal amount')
      }
      
      // Validate default video duration (quick durations: 1, 10, 30 seconds OR standard durations: 1-10 minutes)
      const isQuickDuration = [1, 10, 30].includes(settings.defaultVideoDuration)
      const isStandardDuration = settings.defaultVideoDuration >= 60 && settings.defaultVideoDuration <= 600

      if (!isQuickDuration && !isStandardDuration) {
        throw new Error('Default video duration must be 1, 10, or 30 seconds for quick duration, or between 1-10 minutes (60-600 seconds) for standard duration')
      }
      
      // Save settings to database
      // await saveSystemSettings(settings)
      
      Swal.fire({
        icon: 'success',
        title: 'Settings Saved',
        text: 'System settings have been updated successfully.',
        timer: 2000,
        showConfirmButton: false
      })
    } catch (error: any) {
      console.error('Error saving settings:', error)
      Swal.fire({
        icon: 'error',
        title: 'Save Failed',
        text: error.message || 'Failed to save settings. Please try again.',
      })
    } finally {
      setIsSaving(false)
    }
  }

  const handleInputChange = (field: keyof SystemSettings, value: any) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleEarningRateChange = (plan: keyof SystemSettings['videoEarningRates'], value: number) => {
    setSettings(prev => ({
      ...prev,
      videoEarningRates: {
        ...prev.videoEarningRates,
        [plan]: value
      }
    }))
  }

  if (loading || dataLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="spinner w-12 h-12 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading settings...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center space-x-4">
            <Link
              href="/admin"
              className="text-gray-500 hover:text-gray-700"
            >
              <i className="fas fa-arrow-left text-xl"></i>
            </Link>
            <h1 className="text-2xl font-bold text-gray-900">System Settings</h1>
          </div>
          
          <button
            onClick={handleSaveSettings}
            disabled={isSaving}
            className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg disabled:opacity-50"
          >
            {isSaving ? (
              <>
                <div className="spinner w-4 h-4 mr-2 inline-block"></div>
                Saving...
              </>
            ) : (
              <>
                <i className="fas fa-save mr-2"></i>
                Save Settings
              </>
            )}
          </button>
        </div>
      </header>

      <div className="p-6 space-y-6">
        {/* General Settings */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-bold text-gray-900 mb-4">General Settings</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">App Name</label>
              <input
                type="text"
                value={settings.appName}
                onChange={(e) => handleInputChange('appName', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">App Version</label>
              <input
                type="text"
                value={settings.appVersion}
                onChange={(e) => handleInputChange('appVersion', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Support Email</label>
              <input
                type="email"
                value={settings.supportEmail}
                onChange={(e) => handleInputChange('supportEmail', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Support Phone (WhatsApp)
                <i className="fab fa-whatsapp text-green-500 ml-1"></i>
              </label>
              <input
                type="text"
                value={settings.supportPhone}
                onChange={(e) => handleInputChange('supportPhone', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                placeholder="+917676636990"
              />
              <p className="text-xs text-gray-500 mt-1">WhatsApp number for customer support</p>
            </div>
          </div>
          
          <div className="mt-4 space-y-3">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="maintenanceMode"
                checked={settings.maintenanceMode}
                onChange={(e) => handleInputChange('maintenanceMode', e.target.checked)}
                className="mr-2"
              />
              <label htmlFor="maintenanceMode" className="text-sm font-medium text-gray-700">
                Maintenance Mode (Disable app access for users)
              </label>
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="registrationEnabled"
                checked={settings.registrationEnabled}
                onChange={(e) => handleInputChange('registrationEnabled', e.target.checked)}
                className="mr-2"
              />
              <label htmlFor="registrationEnabled" className="text-sm font-medium text-gray-700">
                Enable New User Registration
              </label>
            </div>
          </div>
        </div>

        {/* Video Settings */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-bold text-gray-900 mb-4">Video Settings</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Default Video Duration</label>
              <select
                value={settings.defaultVideoDuration}
                onChange={(e) => handleInputChange('defaultVideoDuration', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              >
                <optgroup label="🚀 Quick Duration">
                  <option value={1}>1 second</option>
                  <option value={10}>10 seconds</option>
                  <option value={30}>30 seconds</option>
                </optgroup>
                <optgroup label="⏱️ Standard Duration">
                  <option value={60}>1 minute</option>
                  <option value={120}>2 minutes</option>
                  <option value={180}>3 minutes</option>
                  <option value={240}>4 minutes</option>
                  <option value={300}>5 minutes</option>
                  <option value={360}>6 minutes</option>
                  <option value={420}>7 minutes</option>
                  <option value={600}>10 minutes</option>
                </optgroup>
              </select>
              <p className="text-xs text-gray-500 mt-1">
                Default duration for new users: {settings.defaultVideoDuration < 60
                  ? `${settings.defaultVideoDuration} second${settings.defaultVideoDuration > 1 ? 's' : ''}`
                  : `${Math.round(settings.defaultVideoDuration / 60)} minute${Math.round(settings.defaultVideoDuration / 60) > 1 ? 's' : ''}`
                }
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Max Daily Videos</label>
              <input
                type="number"
                min="1"
                max="2000"
                value={settings.maxDailyVideos}
                onChange={(e) => handleInputChange('maxDailyVideos', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>

        {/* Earning Rates */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-bold text-gray-900 mb-4">Video Earning Rates (₹ per video)</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Trial Plan</label>
              <input
                type="number"
                min="0"
                value={settings.videoEarningRates.trial}
                onChange={(e) => handleEarningRateChange('trial', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Starter Plan</label>
              <input
                type="number"
                min="0"
                value={settings.videoEarningRates.starter}
                onChange={(e) => handleEarningRateChange('starter', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Basic Plan</label>
              <input
                type="number"
                min="0"
                value={settings.videoEarningRates.basic}
                onChange={(e) => handleEarningRateChange('basic', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Premium Plan</label>
              <input
                type="number"
                min="0"
                value={settings.videoEarningRates.premium}
                onChange={(e) => handleEarningRateChange('premium', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Gold Plan</label>
              <input
                type="number"
                min="0"
                value={settings.videoEarningRates.gold}
                onChange={(e) => handleEarningRateChange('gold', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Platinum Plan</label>
              <input
                type="number"
                min="0"
                value={settings.videoEarningRates.platinum}
                onChange={(e) => handleEarningRateChange('platinum', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Diamond Plan</label>
              <input
                type="number"
                min="0"
                value={settings.videoEarningRates.diamond}
                onChange={(e) => handleEarningRateChange('diamond', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>

        {/* Withdrawal Settings */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-bold text-gray-900 mb-4">Withdrawal Settings</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Minimum Withdrawal Amount (₹)</label>
              <input
                type="number"
                min="1"
                value={settings.minWithdrawalAmount}
                onChange={(e) => handleInputChange('minWithdrawalAmount', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Maximum Withdrawal Amount (₹)</label>
              <input
                type="number"
                min="1"
                value={settings.maxWithdrawalAmount}
                onChange={(e) => handleInputChange('maxWithdrawalAmount', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Processing Time</label>
              <input
                type="text"
                value={settings.withdrawalProcessingTime}
                onChange={(e) => handleInputChange('withdrawalProcessingTime', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>

        {/* Referral Settings */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-bold text-gray-900 mb-4">Referral Settings</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Referral Bonus (₹)</label>
              <input
                type="number"
                min="0"
                value={settings.referralBonus}
                onChange={(e) => handleInputChange('referralBonus', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              />
              <p className="text-xs text-gray-500 mt-1">Amount given to referrer when someone joins using their code</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
