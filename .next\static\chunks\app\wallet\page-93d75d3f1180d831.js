(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1730],{2846:(e,t,a)=>{"use strict";function s(e){if(console.log("\uD83D\uDD0D safeToDate input:",{value:e,type:typeof e,isDate:e instanceof Date,hasToDate:e&&"object"==typeof e&&"function"==typeof e.toDate,isNull:null===e,isUndefined:void 0===e}),e instanceof Date)return console.log("✅ Returning existing Date object:",e),e;if(e&&"object"==typeof e&&"function"==typeof e.toDate)try{let t=e.toDate();return console.log("✅ Converted Firestore Timestamp:",t),t}catch(e){return console.warn("❌ Failed to convert Firestore Timestamp:",e),new Date}if(e&&("string"==typeof e||"number"==typeof e)){let t=new Date(e);if(!isNaN(t.getTime()))return console.log("✅ Parsed string/number to date:",t),t}return console.error("❌ Invalid date value, cannot convert:",e),new Date("1970-01-01")}function l(e){return s(e).toLocaleDateString()}function i(e){return s(e).toLocaleTimeString()}function n(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Unknown",a=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Unknown";return e&&"string"==typeof e?e:t}(e,t);return a===t?a:a.charAt(0).toUpperCase()+a.slice(1)}a.d(t,{NI:()=>i,cI:()=>n,g1:()=>l,xi:()=>s})},4732:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>p});var s=a(5155),l=a(2115),i=a(6874),n=a.n(i),r=a(6681),c=a(7460),o=a(6572),d=a(3592),m=a(6273),u=a(2846),x=a(8647),h=a(4752),f=a.n(h);function p(){let{user:e,loading:t}=(0,r.Nu)(),{hasBlockingNotifications:i,isChecking:h,markAllAsRead:p}=(0,c.J)((null==e?void 0:e.uid)||null),{isBlocked:b,leaveStatus:w}=(0,o.l)({userId:(null==e?void 0:e.uid)||null,checkInterval:3e4,enabled:!!e}),[N,j]=(0,l.useState)(null),[g,v]=(0,l.useState)([]),[y,k]=(0,l.useState)(!0),[C,D]=(0,l.useState)(""),[S,E]=(0,l.useState)(!1),[P,W]=(0,l.useState)(null),[F,B]=(0,l.useState)(!1),[q,A]=(0,l.useState)({accountHolderName:"",accountNumber:"",ifscCode:"",bankName:""}),[T,I]=(0,l.useState)(!1),[M,R]=(0,l.useState)({allowed:!0}),[U,_]=(0,l.useState)(!1),[H,L]=(0,l.useState)(null);(0,l.useEffect)(()=>{e&&(z(),K(),O(),Y())},[e]),(0,l.useEffect)(()=>{b?R({allowed:!1,reason:w.reason||"Withdrawals are not available due to leave."}):e&&Y()},[b,w,e]);let O=async()=>{try{let t=await (0,d.getUserData)(e.uid);L(t)}catch(e){console.error("Error loading user data:",e)}},Y=async()=>{if(e)try{_(!0);let t=await (0,d.QD)(e.uid);R(t)}catch(e){console.error("Error checking withdrawal eligibility:",e),R({allowed:!1,reason:"Unable to verify withdrawal eligibility. Please try again."})}finally{_(!1)}},z=async()=>{try{k(!0);try{console.log("\uD83D\uDE80 Loading wallet data with optimized functions...");let[t,a]=await Promise.all([(0,d.getWalletData)(e.uid),m.x8.getUserTransactions(e.uid,20,"withdrawal_request")]);j(t);let s=a.map(e=>({id:e.id,type:e.type,amount:"withdrawal_request"===e.type?-Math.abs(e.amount):e.amount,description:e.description,date:(0,u.xi)(e.date),status:e.status||"completed"}));v(s),console.log("✅ Wallet data loaded via optimized functions",s)}catch(l){console.warn("⚠️ Optimized functions failed, using fallback:",l);let[t,a]=await Promise.all([(0,d.getWalletData)(e.uid),(0,d.i8)(e.uid,20)]);j(t);let s=a.map(e=>({id:e.id,type:"withdrawal",amount:-e.amount,description:"Withdrawal request - ₹".concat(e.amount),date:(0,u.xi)(e.date),status:e.status}));v(s),console.log("✅ Wallet data loaded via fallback method",s)}}catch(e){console.error("Error loading wallet data:",e),f().fire({icon:"error",title:"Error",text:"Failed to load wallet data. Please try again."})}finally{k(!1)}},K=async()=>{try{let t=await (0,d.zb)(e.uid);W(t),t&&A(t)}catch(e){console.error("Error loading bank details:",e)}},G=async t=>{if(t.preventDefault(),!T)try{I(!0),await (0,d.mm)(e.uid,q),W(q),B(!1),f().fire({icon:"success",title:"Bank Details Saved",text:"Your bank details have been saved successfully",timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error saving bank details:",e),f().fire({icon:"error",title:"Error",text:e.message||"Failed to save bank details. Please try again."})}finally{I(!1)}},J=e=>{let{name:t,value:a}=e.target;A(e=>({...e,[t]:a}))},Q=async()=>{if(S)return void console.log("⚠️ Withdrawal already in progress, ignoring duplicate request");try{let{isUserPlanExpired:t}=await Promise.resolve().then(a.bind(a,3592)),s=await t(e.uid);if(s.expired)return void f().fire({icon:"error",title:"Plan Expired",html:'\n            <div class="text-center">\n              <p class="mb-2">Your plan has expired and you cannot make withdrawals.</p>\n              <p class="text-sm text-gray-600 mb-2">'.concat(s.reason,'</p>\n              <p class="text-sm text-blue-600">Please upgrade your plan to continue using withdrawal services.</p>\n            </div>\n          '),confirmButtonText:"Go to Plans",showCancelButton:!0,cancelButtonText:"OK"}).then(e=>{e.isConfirmed&&(window.location.href="/plans")})}catch(e){console.error("Error checking plan expiry:",e)}if(b)return void f().fire({icon:"warning",title:"Withdrawal Not Available",text:w.reason||"Withdrawals are not available due to leave.",confirmButtonText:"OK"});let t=parseFloat(C);if(!t||t<=0)return void f().fire({icon:"error",title:"Invalid Amount",text:"Please enter a valid amount to withdraw"});if(t<50)return void f().fire({icon:"error",title:"Minimum Withdrawal",text:"Minimum withdrawal amount is ₹50"});if(t>((null==N?void 0:N.wallet)||0))return void f().fire({icon:"error",title:"Insufficient Balance",text:"You do not have enough balance in your wallet"});if(!P)return void f().fire({icon:"warning",title:"Bank Details Required",text:"Please add your bank details before making a withdrawal"});try{E(!0),console.log("\uD83D\uDD04 Processing withdrawal request for ₹".concat(t)),await (0,d.xj)(e.uid,t,P),await z(),f().fire({icon:"success",title:"Withdrawal Request Submitted",text:"Your withdrawal request for ₹".concat(t," has been submitted and will be processed within 24-48 hours.")}),D(""),console.log("✅ Withdrawal request completed successfully")}catch(e){console.error("❌ Error processing withdrawal:",e),f().fire({icon:"error",title:"Withdrawal Failed",text:e.message||"Failed to process withdrawal request. Please try again."})}finally{E(!1)}},V=e=>null==e||isNaN(e)?"₹0.00":"₹".concat(e.toFixed(2));return t||y||h?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"spinner mb-4"}),(0,s.jsx)("p",{className:"text-white",children:t?"Loading...":h?"Checking notifications...":"Loading wallet..."})]})}):i&&e?(0,s.jsx)(x.A,{userId:e.uid,onAllRead:p}):(0,s.jsxs)("div",{className:"min-h-screen p-4",children:[(0,s.jsx)("header",{className:"glass-card p-4 mb-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)(n(),{href:"/dashboard",className:"glass-button px-4 py-2 text-white",children:[(0,s.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]}),(0,s.jsx)("h1",{className:"text-xl font-bold text-white",children:"My Wallet"}),(0,s.jsxs)("button",{onClick:z,className:"glass-button px-4 py-2 text-white",children:[(0,s.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})}),(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("h3",{className:"text-2xl font-semibold text-white",children:"My Wallet"}),(0,s.jsx)("i",{className:"fas fa-wallet text-green-400 text-3xl"})]}),(0,s.jsx)("p",{className:"text-4xl font-bold text-green-400 mb-2",children:V((null==N?void 0:N.wallet)||0)}),(0,s.jsx)("p",{className:"text-white/60",children:"Total available balance"})]}),(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("h3",{className:"text-lg font-bold text-white",children:[(0,s.jsx)("i",{className:"fas fa-university mr-2"}),"Bank Details"]}),P&&!F&&(0,s.jsxs)("button",{onClick:()=>B(!0),className:"glass-button px-4 py-2 text-white",children:[(0,s.jsx)("i",{className:"fas fa-edit mr-2"}),"Edit"]})]}),P||F?F?(0,s.jsxs)("form",{onSubmit:G,className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"Account Holder Name *"}),(0,s.jsx)("input",{type:"text",name:"accountHolderName",value:q.accountHolderName,onChange:J,className:"form-input",placeholder:"Enter account holder name",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"Bank Name *"}),(0,s.jsx)("input",{type:"text",name:"bankName",value:q.bankName,onChange:J,className:"form-input",placeholder:"Enter bank name",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"Account Number *"}),(0,s.jsx)("input",{type:"text",name:"accountNumber",value:q.accountNumber,onChange:J,className:"form-input",placeholder:"Enter account number",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"IFSC Code *"}),(0,s.jsx)("input",{type:"text",name:"ifscCode",value:q.ifscCode,onChange:J,className:"form-input",placeholder:"Enter IFSC code (e.g., SBIN0001234)",required:!0})]})]}),(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)("button",{type:"submit",disabled:T,className:"".concat(T?"btn-disabled cursor-not-allowed opacity-50":"btn-primary hover:bg-blue-600"),children:T?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Saving Bank Details..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-save mr-2"}),"Save Bank Details"]})}),(0,s.jsx)("button",{type:"button",onClick:()=>B(!1),className:"btn-secondary",children:"Cancel"})]})]}):(0,s.jsx)("div",{className:"bg-white/10 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-white/60 text-sm",children:"Account Holder"}),(0,s.jsx)("p",{className:"text-white font-medium",children:null==P?void 0:P.accountHolderName})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-white/60 text-sm",children:"Bank Name"}),(0,s.jsx)("p",{className:"text-white font-medium",children:null==P?void 0:P.bankName})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-white/60 text-sm",children:"Account Number"}),(0,s.jsxs)("p",{className:"text-white font-medium",children:["****",null==P?void 0:P.accountNumber.slice(-4)]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-white/60 text-sm",children:"IFSC Code"}),(0,s.jsx)("p",{className:"text-white font-medium",children:null==P?void 0:P.ifscCode})]})]})}):(0,s.jsxs)("div",{className:"text-center py-6",children:[(0,s.jsx)("i",{className:"fas fa-university text-white/30 text-4xl mb-4"}),(0,s.jsx)("p",{className:"text-white/60 mb-4",children:"No bank details added yet"}),(0,s.jsxs)("button",{onClick:()=>B(!0),className:"btn-primary",children:[(0,s.jsx)("i",{className:"fas fa-plus mr-2"}),"Add Bank Details"]})]})]}),(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("h3",{className:"text-lg font-bold text-white mb-4",children:[(0,s.jsx)("i",{className:"fas fa-money-bill-wave mr-2"}),"Withdraw Funds"]}),(null==H?void 0:H.plan)==="Trial"&&(0,s.jsxs)("div",{className:"mb-4 p-4 bg-red-500/20 border border-red-500/30 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center mb-2",children:[(0,s.jsx)("i",{className:"fas fa-lock text-red-400 mr-2"}),(0,s.jsx)("span",{className:"text-red-400 font-medium",children:"Trial Plan Restriction"})]}),(0,s.jsx)("p",{className:"text-white/80 text-sm mb-3",children:"Trial plan users cannot make withdrawals. Please upgrade to a paid plan to enable withdrawal functionality."}),(0,s.jsx)("div",{className:"flex gap-3",children:(0,s.jsxs)(n(),{href:"/plans",className:"btn-primary inline-block",children:[(0,s.jsx)("i",{className:"fas fa-arrow-up mr-2"}),"Upgrade Plan"]})})]}),(0,s.jsxs)("div",{className:"mb-4 p-4 bg-blue-500/20 border border-blue-500/30 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center mb-2",children:[(0,s.jsx)("i",{className:"fas fa-clock text-blue-400 mr-2"}),(0,s.jsx)("span",{className:"text-blue-400 font-medium",children:"Withdrawal Timings"})]}),(0,s.jsxs)("p",{className:"text-white/80 text-sm mb-2",children:["Withdrawals are only allowed between ",(0,s.jsx)("strong",{children:"10:00 AM to 6:00 PM"})," on non-leave days."]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("p",{className:"text-white/60 text-xs",children:["Current time: ",new Date().toLocaleTimeString()," | Status: ",M.allowed?(0,s.jsx)("span",{className:"text-green-400 font-medium",children:"✓ Available"}):(0,s.jsx)("span",{className:"text-red-400 font-medium",children:"✗ Not Available"})]}),(0,s.jsx)("button",{onClick:Y,disabled:U,className:"text-blue-400 hover:text-blue-300 text-xs",children:U?(0,s.jsx)("div",{className:"spinner w-3 h-3"}):(0,s.jsx)("i",{className:"fas fa-sync-alt"})})]}),!M.allowed&&M.reason&&(0,s.jsxs)("p",{className:"text-red-400 text-sm mt-2",children:[(0,s.jsx)("i",{className:"fas fa-exclamation-triangle mr-1"}),M.reason]})]}),P?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,s.jsx)("input",{type:"number",value:C,onChange:e=>D(e.target.value),placeholder:"Enter amount to withdraw (Min: ₹50)",className:"form-input flex-1",min:"50",max:(null==N?void 0:N.wallet)||0}),(0,s.jsx)("button",{onClick:Q,disabled:S||!C||!M.allowed||0>=parseFloat(C),className:"whitespace-nowrap ".concat(S?"btn-disabled cursor-not-allowed opacity-50 pointer-events-none":M.allowed&&C&&parseFloat(C)>0?"btn-primary hover:bg-blue-600":"btn-disabled cursor-not-allowed opacity-50"),style:{pointerEvents:S?"none":"auto"},children:S?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Processing Withdrawal..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-download mr-2"}),"Withdraw ₹",C||"0"]})})]}),(0,s.jsxs)("p",{className:"text-white/60 text-sm mt-2",children:["Available: ",V((null==N?void 0:N.wallet)||0)," | Minimum: ₹50"]})]}):(0,s.jsxs)("div",{className:"text-center py-6",children:[(0,s.jsx)("i",{className:"fas fa-exclamation-triangle text-yellow-400 text-3xl mb-4"}),(0,s.jsx)("p",{className:"text-white/60 mb-4",children:"Please add your bank details before making a withdrawal"}),(0,s.jsxs)("button",{onClick:()=>B(!0),className:"btn-primary",children:[(0,s.jsx)("i",{className:"fas fa-university mr-2"}),"Add Bank Details"]})]})]}),(0,s.jsxs)("div",{className:"glass-card p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("h3",{className:"text-lg font-bold text-white",children:[(0,s.jsx)("i",{className:"fas fa-money-bill-wave mr-2"}),"Withdrawal History"]}),(0,s.jsxs)("button",{onClick:z,className:"glass-button px-4 py-2 text-white",children:[(0,s.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]}),0===g.length?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("i",{className:"fas fa-money-bill-wave text-white/30 text-4xl mb-4"}),(0,s.jsx)("p",{className:"text-white/60 mb-2",children:"No withdrawal requests yet"}),(0,s.jsx)("p",{className:"text-white/40 text-sm",children:"Your withdrawal requests will appear here"})]}):(0,s.jsx)("div",{className:"space-y-3",children:g.map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 bg-white/10 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("i",{className:"fas fa-money-bill-wave text-red-400 mr-3"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-white font-medium",children:e.description}),(0,s.jsxs)("p",{className:"text-white/60 text-sm",children:[(0,u.g1)(e.date)," at ",(0,u.NI)(e.date)]})]})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsx)("p",{className:"font-bold text-red-400",children:V(Math.abs(e.amount))}),(0,s.jsx)("span",{className:"text-xs px-2 py-1 rounded-full ".concat("pending"===e.status?"bg-yellow-500/20 text-yellow-400":"approved"===e.status?"bg-green-500/20 text-green-400":"rejected"===e.status?"bg-red-500/20 text-red-400":"completed"===e.status?"bg-blue-500/20 text-blue-400":"bg-gray-500/20 text-gray-400"),children:"pending"===e.status?"⏳ Pending":"approved"===e.status?"✅ Approved":"rejected"===e.status?"❌ Rejected":"completed"===e.status?"✅ Completed":e.status})]})]},e.id))})]})]})}},6034:(e,t,a)=>{Promise.resolve().then(a.bind(a,4732))}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,8818,6874,3592,6681,6119,8441,1684,7358],()=>t(6034)),_N_E=e.O()}]);