(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[993,6779,9160],{3737:(e,t,a)=>{"use strict";function i(e,t,a){if(!e||0===e.length)return void alert("No data to export");let i=a||Object.keys(e[0]),s=["Account Number","Mobile Number","Mobile","Phone","Contact","User ID","Referral Code","IFSC Code","Bank Account","Account No"],o=new Blob(["\uFEFF"+[i.join(","),...e.map(e=>i.map(t=>{let a=e[t];if(null==a)return"";let i=s.some(e=>t.toLowerCase().includes(e.toLowerCase()));if("string"==typeof a){let e=a.replace(/"/g,'""');return'"'.concat(e,'"')}return a instanceof Date?'"'.concat(a.toLocaleDateString(),'"'):"object"==typeof a&&null!==a&&a.toDate?'"'.concat(a.toDate().toLocaleDateString(),'"'):i&&("number"==typeof a||!isNaN(Number(a)))?'"'.concat(a,'"'):"number"==typeof a?a.toString():'"'.concat(String(a),'"')}).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),n=document.createElement("a");if(void 0!==n.download){let e=URL.createObjectURL(o);n.setAttribute("href",e),n.setAttribute("download","".concat(t,"_").concat(new Date().toISOString().split("T")[0],".csv")),n.style.visibility="hidden",document.body.appendChild(n),n.click(),document.body.removeChild(n)}}function s(e){return e.map(e=>({"User ID":e.id||"",Name:e.name||"",Email:e.email||"",Mobile:String(e.mobile||""),"Referral Code":e.referralCode||"","Referred By":e.referredBy||"Direct",Plan:e.plan||"","Plan Expiry":e.planExpiry instanceof Date?e.planExpiry.toLocaleDateString():e.planExpiry?new Date(e.planExpiry).toLocaleDateString():"","Active Days":e.activeDays||0,"Total Videos":e.totalVideos||0,"Today Videos":e.todayVideos||0,"Last Video Date":e.lastVideoDate instanceof Date?e.lastVideoDate.toLocaleDateString():e.lastVideoDate?new Date(e.lastVideoDate).toLocaleDateString():"","Video Duration (seconds)":e.videoDuration||300,"Quick Video Advantage":e.quickVideoAdvantage?"Yes":"No","Quick Video Advantage Expiry":e.quickVideoAdvantageExpiry instanceof Date?e.quickVideoAdvantageExpiry.toLocaleDateString():e.quickVideoAdvantageExpiry?new Date(e.quickVideoAdvantageExpiry).toLocaleDateString():"","Quick Video Remaining Days":e.quickVideoAdvantageRemainingDays||0,"Quick Video Advantage Granted By":e.quickVideoAdvantageGrantedBy||"","Wallet Balance":e.wallet||0,"Referral Bonus Credited":e.referralBonusCredited?"Yes":"No",Status:e.status||"","Joined Date":e.joinedDate instanceof Date?e.joinedDate.toLocaleDateString():e.joinedDate?new Date(e.joinedDate).toLocaleDateString():"","Joined Time":e.joinedDate instanceof Date?e.joinedDate.toLocaleTimeString():e.joinedDate?new Date(e.joinedDate).toLocaleTimeString():""}))}function o(e){return e.map(e=>({"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","User Mobile":String(e.userMobile||""),Type:e.type||"",Amount:e.amount||0,Description:e.description||"",Status:e.status||"",Date:e.date instanceof Date?e.date.toLocaleDateString():e.date?new Date(e.date).toLocaleDateString():"",Time:e.date instanceof Date?e.date.toLocaleTimeString():e.date?new Date(e.date).toLocaleTimeString():""}))}function n(e){return e.map(e=>{var t,a,i,s;return{"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","Mobile Number":String(e.userMobile||""),"User Plan":e.userPlan||"","Active Days":e.userActiveDays||0,"Wallet Balance":e.walletBalance||0,"Withdrawal Amount":e.amount||0,"Account Holder Name":(null==(t=e.bankDetails)?void 0:t.accountHolderName)||"","Bank Name":(null==(a=e.bankDetails)?void 0:a.bankName)||"","Account Number":String((null==(i=e.bankDetails)?void 0:i.accountNumber)||""),"IFSC Code":(null==(s=e.bankDetails)?void 0:s.ifscCode)||"",Status:e.status||"pending","Request Date":e.requestDate instanceof Date?e.requestDate.toLocaleDateString():e.requestDate?new Date(e.requestDate).toLocaleDateString():"","Request Time":e.requestDate instanceof Date?e.requestDate.toLocaleTimeString():e.requestDate?new Date(e.requestDate).toLocaleTimeString():"","Admin Notes":e.adminNotes||""}})}function r(e){return e.map(e=>({Title:e.title,Message:e.message,Type:e.type,Target:e.target,Status:e.status,"Created Date":e.createdAt instanceof Date?e.createdAt.toLocaleDateString():e.createdAt?new Date(e.createdAt).toLocaleDateString():"","Sent Date":e.sentAt instanceof Date?e.sentAt.toLocaleDateString():e.sentAt?new Date(e.sentAt).toLocaleDateString():""}))}a.d(t,{Bf:()=>i,Fz:()=>s,Pe:()=>r,dB:()=>n,sL:()=>o})},4880:(e,t,a)=>{Promise.resolve().then(a.bind(a,9119))},6273:(e,t,a)=>{"use strict";a.d(t,{x8:()=>y});var i=a(2144),s=a(6104);let o=(0,i.Qg)(s.Cn,"getUserDashboardData"),n=(0,i.Qg)(s.Cn,"submitVideoBatch"),r=(0,i.Qg)(s.Cn,"processWithdrawalRequest"),l=(0,i.Qg)(s.Cn,"getUserNotifications"),c=(0,i.Qg)(s.Cn,"getUserTransactions"),d=(0,i.Qg)(s.Cn,"getAdminWithdrawals"),u=(0,i.Qg)(s.Cn,"getAdminDashboardStats"),m=(0,i.Qg)(s.Cn,"getAdminUsers"),g=(0,i.Qg)(s.Cn,"getAdminNotifications"),f=(0,i.Qg)(s.Cn,"createAdminNotification"),h=new Map;async function p(e){try{console.log("\uD83D\uDE80 Using optimized dashboard data function for user:",e),console.log("\uD83D\uDD17 Functions instance:",s.Cn.app.options.projectId);let t=new Promise((e,t)=>{setTimeout(()=>t(Error("Function timeout")),5e3)}),a=await Promise.race([o({userId:e}),t]);if(console.log("\uD83D\uDCE1 Function response received:",a),a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success){console.log("✅ Dashboard data loaded via optimized function");let t=e.data;return{userData:{name:t.user.name,email:t.user.email,mobile:t.user.mobile,referralCode:t.user.referralCode,plan:t.user.plan,planExpiry:null,activeDays:t.user.activeDays},walletData:{wallet:t.user.wallet},videoData:{totalVideos:t.videos.total,todayVideos:t.videos.today,remainingVideos:t.videos.remaining}}}throw console.error("❌ Function returned success: false",e),Error("Function returned success: false")}throw console.error("❌ Invalid function response structure:",a),Error("Invalid response from dashboard function")}catch(e){throw console.error("❌ Error in optimized dashboard data:",e),console.error("❌ Error details:",{name:null==e?void 0:e.name,message:null==e?void 0:e.message,code:null==e?void 0:e.code,details:null==e?void 0:e.details}),e}}async function x(){try{console.log("\uD83D\uDE80 Using optimized admin dashboard stats function...");let e=await u({});if(e.data&&"object"==typeof e.data&&"success"in e.data){let t=e.data;if(t.success)return console.log("✅ Admin dashboard stats loaded via optimized function"),t.data}throw Error("Invalid response from admin dashboard stats function")}catch(e){throw console.error("❌ Error in optimized admin dashboard stats:",e),e}}let y={getDashboardData:async function(e){let t="dashboard-".concat(e),i=Date.now(),s=h.get(t);if(s&&i-s.timestamp<s.ttl)return console.log("\uD83C\uDFAF Using cached fallback dashboard data"),s.data;try{let a=await p(e);return h.set(t,{data:a,timestamp:i,ttl:6e4}),a}catch(g){console.warn("⚠️ Optimized function failed, falling back to direct calls:",g instanceof Error?g.message:String(g));let{getUserData:s,getWalletData:o,getVideoCountData:n}=await a.e(3592).then(a.bind(a,3592));console.log("\uD83D\uDCCA Loading dashboard data via direct Firestore calls...");let r=Date.now(),[l,c,d]=await Promise.all([s(e),o(e),n(e)]),u=Date.now()-r;console.log("✅ Dashboard data loaded via fallback in ".concat(u,"ms"));let m={userData:l,walletData:c,videoData:d};return h.set(t,{data:m,timestamp:i,ttl:3e4}),m}},submitVideoBatch:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;try{console.log("\uD83D\uDE80 Using optimized video batch submission...");let a=await n({userId:e,videoCount:t});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Video batch submitted via optimized function"),e.data}throw Error("Invalid response from video batch function")}catch(e){throw console.error("❌ Error in optimized video batch submission:",e),e}},processWithdrawal:async function(e){try{console.log("\uD83D\uDE80 Using optimized withdrawal processing...");let t=await r(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Withdrawal processed via optimized function"),e.data}throw Error("Invalid response from withdrawal function")}catch(e){throw console.error("❌ Error in optimized withdrawal processing:",e),e}},getUserNotifications:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;try{console.log("\uD83D\uDE80 Using optimized notifications function...");let a=await l({userId:e,limit:t});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Notifications loaded via optimized function"),e.data}throw Error("Invalid response from notifications function")}catch(e){throw console.error("❌ Error in optimized notifications:",e),e}},getUserTransactions:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"all";try{console.log("\uD83D\uDE80 Using optimized transactions function...");let i=await c({userId:e,limit:t,type:a});if(i.data&&"object"==typeof i.data&&"success"in i.data){let e=i.data;if(e.success)return console.log("✅ Transactions loaded via optimized function"),e.data}throw Error("Invalid response from transactions function")}catch(e){throw console.error("❌ Error in optimized transactions:",e),e}},getAdminWithdrawals:async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{console.log("\uD83D\uDE80 Using optimized admin withdrawals function, showAll:",e),console.log("\uD83D\uDD17 Functions instance:",s.Cn.app.options.projectId);let t=await d({showAllWithdrawals:e});if(console.log("\uD83D\uDCE1 Admin withdrawals function response received:",t),t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin withdrawals loaded via optimized function"),e.data;throw console.error("❌ Admin withdrawals function returned success: false",e),Error("Admin withdrawals function returned success: false")}throw console.error("❌ Invalid admin withdrawals function response structure:",t),Error("Invalid response from admin withdrawals function")}catch(e){throw console.error("❌ Error in optimized admin withdrawals:",e),console.error("❌ Error details:",{name:null==e?void 0:e.name,message:null==e?void 0:e.message,code:null==e?void 0:e.code,details:null==e?void 0:e.details}),e}},getAdminDashboardStats:async function(){try{return await x()}catch(t){console.warn("⚠️ Optimized admin stats function failed, falling back to direct calls");let{getAdminDashboardStats:e}=await Promise.all([a.e(3592),a.e(6779)]).then(a.bind(a,6779));return await e()}},getAdminUsers:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{console.log("\uD83D\uDE80 Using optimized admin users function...");let t=await m(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin users loaded via optimized function"),e.data}throw Error("Invalid response from admin users function")}catch(e){throw console.error("❌ Error in optimized admin users:",e),e}},getAdminNotifications:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"all";try{console.log("\uD83D\uDE80 Using optimized admin notifications function...");let a=await g({limit:e,type:t});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Admin notifications loaded via optimized function"),e.data}throw Error("Invalid response from admin notifications function")}catch(e){throw console.error("❌ Error in optimized admin notifications:",e),e}},createAdminNotification:async function(e){try{console.log("\uD83D\uDE80 Using optimized admin notification creation...");let t=await f(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin notification created via optimized function"),e.data}throw Error("Invalid response from admin notification creation function")}catch(e){throw console.error("❌ Error in optimized admin notification creation:",e),e}},areFunctionsAvailable:async function(){try{console.log("\uD83D\uDD0D Testing Firebase Functions connectivity..."),console.log("\uD83D\uDD17 Functions project:",s.Cn.app.options.projectId),console.log("\uD83D\uDD17 Functions region:",s.Cn.region);let e=await o({userId:"test"});return console.log("✅ Functions are available, test response:",e),!0}catch(e){return console.warn("⚠️ Firebase Functions not available, falling back to direct Firestore"),console.error("❌ Functions test error:",{name:null==e?void 0:e.name,message:null==e?void 0:e.message,code:null==e?void 0:e.code,details:null==e?void 0:e.details}),!1}}}},6779:(e,t,a)=>{"use strict";a.d(t,{CF:()=>d,I0:()=>m,TK:()=>p,getAdminDashboardStats:()=>r,getAllPendingWithdrawals:()=>g,getAllWithdrawals:()=>f,hG:()=>x,lo:()=>l,nQ:()=>u,r2:()=>h,updateWithdrawalStatus:()=>y,x5:()=>c});var i=a(5317),s=a(6104),o=a(3592);let n=new Map;async function r(){let e="dashboard-stats",t=function(e){let t=n.get(e);return t&&Date.now()-t.timestamp<3e5?t.data:null}(e);if(t)return t;try{let t=new Date;t.setHours(0,0,0,0);let a=i.Dc.fromDate(t),r=await (0,i.getDocs)((0,i.collection)(s.db,o.COLLECTIONS.users)),l=r.size,c=(0,i.P)((0,i.collection)(s.db,o.COLLECTIONS.users),(0,i._M)(o.FIELD_NAMES.joinedDate,">=",a)),d=(await (0,i.getDocs)(c)).size,u=0,m=0,g=0,f=0;r.forEach(e=>{var a;let i=e.data();u+=i[o.FIELD_NAMES.totalVideos]||0,m+=i[o.FIELD_NAMES.wallet]||0;let s=null==(a=i[o.FIELD_NAMES.lastVideoDate])?void 0:a.toDate();s&&s.toDateString()===t.toDateString()&&(g+=i[o.FIELD_NAMES.todayVideos]||0)});try{let e=(0,i.P)((0,i.collection)(s.db,o.COLLECTIONS.transactions),(0,i._M)(o.FIELD_NAMES.type,"==","video_earning"),(0,i.AB)(1e3));(await (0,i.getDocs)(e)).forEach(e=>{var a;let i=e.data(),s=null==(a=i[o.FIELD_NAMES.date])?void 0:a.toDate();s&&s>=t&&(f+=i[o.FIELD_NAMES.amount]||0)})}catch(e){console.warn("Could not fetch today's transactions:",e)}let h=(0,i.P)((0,i.collection)(s.db,o.COLLECTIONS.withdrawals),(0,i._M)("status","==","pending")),p=(await (0,i.getDocs)(h)).size,x=(0,i.P)((0,i.collection)(s.db,o.COLLECTIONS.withdrawals),(0,i._M)("date",">=",a)),y=(await (0,i.getDocs)(x)).size,w={totalUsers:l,totalVideos:u,totalEarnings:m,pendingWithdrawals:p,todayUsers:d,todayVideos:g,todayEarnings:f,todayWithdrawals:y};return n.set(e,{data:w,timestamp:Date.now()}),w}catch(e){throw console.error("Error getting admin dashboard stats:",e),e}}async function l(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,i.P)((0,i.collection)(s.db,o.COLLECTIONS.users),(0,i.My)(o.FIELD_NAMES.joinedDate,"desc"),(0,i.AB)(e));t&&(a=(0,i.P)((0,i.collection)(s.db,o.COLLECTIONS.users),(0,i.My)(o.FIELD_NAMES.joinedDate,"desc"),(0,i.HM)(t),(0,i.AB)(e)));let n=await (0,i.getDocs)(a);return{users:n.docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[o.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[o.FIELD_NAMES.planExpiry])?void 0:a.toDate()}}),lastDoc:n.docs[n.docs.length-1]||null,hasMore:n.docs.length===e}}catch(e){throw console.error("Error getting users:",e),e}}async function c(e){try{if(!e||0===e.trim().length)return[];let t=e.toLowerCase().trim(),a=(0,i.P)((0,i.collection)(s.db,o.COLLECTIONS.users),(0,i.My)(o.FIELD_NAMES.joinedDate,"desc"));return(await (0,i.getDocs)(a)).docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[o.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[o.FIELD_NAMES.planExpiry])?void 0:a.toDate()}}).filter(e=>{let a=String(e[o.FIELD_NAMES.name]||"").toLowerCase(),i=String(e[o.FIELD_NAMES.email]||"").toLowerCase(),s=String(e[o.FIELD_NAMES.mobile]||"").toLowerCase(),n=String(e[o.FIELD_NAMES.referralCode]||"").toLowerCase();return a.includes(t)||i.includes(t)||s.includes(t)||n.includes(t)})}catch(e){throw console.error("Error searching users:",e),e}}async function d(){try{let e=(0,i.P)((0,i.collection)(s.db,o.COLLECTIONS.users),(0,i.My)(o.FIELD_NAMES.joinedDate,"desc"));return(await (0,i.getDocs)(e)).docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[o.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[o.FIELD_NAMES.planExpiry])?void 0:a.toDate()}})}catch(e){throw console.error("Error getting all users:",e),e}}async function u(){try{let e=(0,i.P)((0,i.collection)(s.db,o.COLLECTIONS.users));return(await (0,i.getDocs)(e)).size}catch(e){throw console.error("Error getting total user count:",e),e}}async function m(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,i.P)((0,i.collection)(s.db,o.COLLECTIONS.transactions),(0,i.My)(o.FIELD_NAMES.date,"desc"),(0,i.AB)(e));t&&(a=(0,i.P)((0,i.collection)(s.db,o.COLLECTIONS.transactions),(0,i.My)(o.FIELD_NAMES.date,"desc"),(0,i.HM)(t),(0,i.AB)(e)));let n=await (0,i.getDocs)(a);return{transactions:n.docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data()[o.FIELD_NAMES.date])?void 0:t.toDate()}}),lastDoc:n.docs[n.docs.length-1]||null,hasMore:n.docs.length===e}}catch(e){throw console.error("Error getting transactions:",e),e}}async function g(){try{console.log("\uD83D\uDD0D Loading ALL pending withdrawals...");let e=(0,i.P)((0,i.collection)(s.db,o.COLLECTIONS.withdrawals),(0,i._M)("status","==","pending"),(0,i.My)("date","desc")),t=(await (0,i.getDocs)(e)).docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data().date)?void 0:t.toDate()}});return console.log("✅ Loaded ".concat(t.length," pending withdrawals")),t}catch(e){throw console.error("Error getting all pending withdrawals:",e),e}}async function f(){try{console.log("\uD83D\uDD0D Loading ALL withdrawals...");let e=(0,i.P)((0,i.collection)(s.db,o.COLLECTIONS.withdrawals),(0,i.My)("date","desc")),t=(await (0,i.getDocs)(e)).docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data().date)?void 0:t.toDate()}});return console.log("✅ Loaded ".concat(t.length," total withdrawals")),t}catch(e){throw console.error("Error getting all withdrawals:",e),e}}async function h(){try{console.log("\uD83D\uDD0D Loading ALL transactions...");let e=(0,i.P)((0,i.collection)(s.db,o.COLLECTIONS.transactions),(0,i.My)(o.FIELD_NAMES.date,"desc")),t=(await (0,i.getDocs)(e)).docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data()[o.FIELD_NAMES.date])?void 0:t.toDate()}});return console.log("✅ Loaded ".concat(t.length," total transactions")),t}catch(e){throw console.error("Error getting all transactions:",e),e}}async function p(e,t){try{await (0,i.mZ)((0,i.H9)(s.db,o.COLLECTIONS.users,e),t),n.delete("dashboard-stats")}catch(e){throw console.error("Error updating user:",e),e}}async function x(e){try{await (0,i.kd)((0,i.H9)(s.db,o.COLLECTIONS.users,e)),n.delete("dashboard-stats")}catch(e){throw console.error("Error deleting user:",e),e}}async function y(e,t,r){try{let l=await (0,i.x7)((0,i.H9)(s.db,o.COLLECTIONS.withdrawals,e));if(!l.exists())throw Error("Withdrawal not found");let{userId:c,amount:d,status:u}=l.data(),m={status:t,updatedAt:i.Dc.now()};if(r&&(m.adminNotes=r),await (0,i.mZ)((0,i.H9)(s.db,o.COLLECTIONS.withdrawals,e),m),"approved"===t&&"approved"!==u){let{addTransaction:e}=await Promise.resolve().then(a.bind(a,3592));await e(c,{type:"withdrawal_approved",amount:0,description:"Withdrawal approved - ₹".concat(d," processed for transfer")})}if("rejected"===t&&"rejected"!==u){let{updateWalletBalance:e,addTransaction:t}=await Promise.resolve().then(a.bind(a,3592));await e(c,d),await t(c,{type:"withdrawal_rejected",amount:d,description:"Withdrawal rejected - ₹".concat(d," credited back to wallet")})}n.delete("dashboard-stats")}catch(e){throw console.error("Error updating withdrawal status:",e),e}}},9119:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>f});var i=a(5155),s=a(2115),o=a(6874),n=a.n(o),r=a(6681),l=a(3592),c=a(6273),d=a(6779),u=a(3737),m=a(4752),g=a.n(m);function f(){console.log("\uD83D\uDD0D AdminNotificationsPage component loaded");let{user:e,loading:t,isAdmin:a}=(0,r.wC)(),[o,m]=(0,s.useState)([]),[f,h]=(0,s.useState)([]),[p,x]=(0,s.useState)(!0),[y,w]=(0,s.useState)(!1),[b,v]=(0,s.useState)(!1),[D,N]=(0,s.useState)([]),[j,E]=(0,s.useState)(!1);console.log("\uD83D\uDD0D Render check - loading:",t,"dataLoading:",p,"isAdmin:",a);let[S,C]=(0,s.useState)({title:"",message:"",type:"info",targetUsers:"all",selectedUserIds:[]});(0,s.useEffect)(()=>{a&&A()},[a]);let A=async()=>{try{x(!0);try{console.log("\uD83D\uDE80 Loading admin notifications with optimized functions...");let[e,t]=await Promise.all([c.x8.getAdminNotifications(50,"all"),c.x8.getAdminUsers({limit:100})]),a=e.map(e=>{var t,a;return{...e,createdAt:e.createdAt instanceof Date?e.createdAt:(null==(a=e.createdAt)||null==(t=a.toDate)?void 0:t.call(a))||new Date(e.createdAt||Date.now())}});m(a),h(t.users),console.log("✅ Admin notifications loaded via optimized functions")}catch(i){console.warn("⚠️ Optimized functions failed, using fallback:",i);let[e,t]=await Promise.all([(0,l._f)(50),(0,d.lo)()]),a=e.map(e=>{var t,a;return{...e,createdAt:e.createdAt instanceof Date?e.createdAt:(null==(a=e.createdAt)||null==(t=a.toDate)?void 0:t.call(a))||new Date(e.createdAt||Date.now())}});m(a),h(t.users)}}catch(e){console.error("Error loading data:",e),g().fire({icon:"error",title:"Error",text:"Failed to load data. Please try again."})}finally{x(!1)}},L=async()=>{try{v(!0);try{await c.x8.createAdminNotification({title:"Test Notification",message:"This is a test notification to verify the system is working correctly.",type:"info",targetUsers:"all",userIds:[]})}catch(t){console.warn("⚠️ Optimized notification creation failed, using fallback:",t),await (0,l.z8)({title:"Test Notification",message:"This is a test notification to verify the system is working correctly.",type:"info",targetUsers:"all",userIds:[],createdBy:(null==e?void 0:e.email)||"admin"})}g().fire({icon:"success",title:"Test Notification Sent!",text:"Test notification sent to all users. Check user dashboards to verify delivery.",timer:3e3,showConfirmButton:!1}),A()}catch(e){console.error("Error sending test notification:",e),g().fire({icon:"error",title:"Test Failed",text:"Failed to send test notification. Please try again."})}finally{v(!1)}},I=async()=>{try{if(!S.title.trim()||!S.message.trim())return void g().fire({icon:"error",title:"Validation Error",text:"Please fill in both title and message."});if("specific"===S.targetUsers&&0===S.selectedUserIds.length)return void g().fire({icon:"error",title:"Validation Error",text:"Please select at least one user for specific targeting."});v(!0),console.log("Sending notification:",{title:S.title.trim(),message:S.message.trim(),type:S.type,targetUsers:S.targetUsers,userIds:"specific"===S.targetUsers?S.selectedUserIds:[],createdBy:(null==e?void 0:e.email)||"admin"});try{await c.x8.createAdminNotification({title:S.title.trim(),message:S.message.trim(),type:S.type,targetUsers:S.targetUsers,userIds:"specific"===S.targetUsers?S.selectedUserIds:[]})}catch(t){console.warn("⚠️ Optimized notification creation failed, using fallback:",t),await (0,l.z8)({title:S.title.trim(),message:S.message.trim(),type:S.type,targetUsers:S.targetUsers,userIds:"specific"===S.targetUsers?S.selectedUserIds:[],createdBy:(null==e?void 0:e.email)||"admin"})}g().fire({icon:"success",title:"Notification Sent!",text:"Notification sent to ".concat("all"===S.targetUsers?"all users":"".concat(S.selectedUserIds.length," selected users"),"."),timer:3e3,showConfirmButton:!1}),C({title:"",message:"",type:"info",targetUsers:"all",selectedUserIds:[]}),w(!1),A()}catch(e){console.error("Error sending notification:",e),g().fire({icon:"error",title:"Send Failed",text:"Failed to send notification. Please try again."})}finally{v(!1)}},U=e=>{switch(e){case"success":return"fas fa-check-circle text-green-500";case"warning":return"fas fa-exclamation-triangle text-yellow-500";case"error":return"fas fa-times-circle text-red-500";default:return"fas fa-info-circle text-blue-500"}},k=e=>{let t,a=new Date;t=e instanceof Date?e:e&&"function"==typeof e.toDate?e.toDate():e&&"string"==typeof e||e&&"number"==typeof e?new Date(e):new Date;let i=Math.floor((a.getTime()-t.getTime())/1e3);if(i<60)return"Just now";if(i<3600){let e=Math.floor(i/60);return"".concat(e," minute").concat(e>1?"s":""," ago")}if(i<86400){let e=Math.floor(i/3600);return"".concat(e," hour").concat(e>1?"s":""," ago")}{let e=Math.floor(i/86400);return"".concat(e," day").concat(e>1?"s":""," ago")}},F=async(e,t)=>{if((await g().fire({icon:"warning",title:"Delete Notification",text:'Are you sure you want to delete "'.concat(t,'"? This action cannot be undone.'),showCancelButton:!0,confirmButtonText:"Yes, Delete",confirmButtonColor:"#dc2626",cancelButtonText:"Cancel"})).isConfirmed)try{E(!0),await (0,l.fP)(e),m(t=>t.filter(t=>t.id!==e)),g().fire({icon:"success",title:"Notification Deleted",text:"Notification has been deleted successfully",timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error deleting notification:",e),g().fire({icon:"error",title:"Delete Failed",text:"Failed to delete notification. Please try again."})}finally{E(!1)}},T=async()=>{if(0===D.length)return void g().fire({icon:"warning",title:"No Selection",text:"Please select notifications to delete."});if((await g().fire({icon:"warning",title:"Delete Selected Notifications",text:"Are you sure you want to delete ".concat(D.length," selected notifications? This action cannot be undone."),showCancelButton:!0,confirmButtonText:"Yes, Delete All",confirmButtonColor:"#dc2626",cancelButtonText:"Cancel"})).isConfirmed)try{E(!0),await Promise.all(D.map(e=>(0,l.fP)(e))),m(e=>e.filter(e=>!D.includes(e.id))),N([]),g().fire({icon:"success",title:"Notifications Deleted",text:"".concat(D.length," notifications have been deleted successfully"),timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error deleting notifications:",e),g().fire({icon:"error",title:"Delete Failed",text:"Failed to delete some notifications. Please try again."})}finally{E(!1)}},M=e=>{N(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])};return t||p?(0,i.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Loading notifications..."})]})}):(0,i.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,i.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,i.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsx)(n(),{href:"/admin",className:"text-gray-500 hover:text-gray-700",children:(0,i.jsx)("i",{className:"fas fa-arrow-left text-xl"})}),(0,i.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Notifications"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsxs)("span",{className:"text-gray-700",children:["Total: ",o.length,D.length>0&&(0,i.jsxs)("span",{className:"ml-2 text-blue-600",children:["(",D.length," selected)"]})]}),D.length>0&&(0,i.jsxs)("button",{onClick:T,disabled:j,className:"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:[(0,i.jsx)("i",{className:"fas fa-trash mr-2"}),"Delete Selected (",D.length,")"]}),(0,i.jsxs)("button",{onClick:L,disabled:b||j,className:"bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:[(0,i.jsx)("i",{className:"fas fa-vial mr-2"}),"Test Notification"]}),(0,i.jsxs)("button",{onClick:()=>{if(0===o.length)return void g().fire({icon:"warning",title:"No Data",text:"No notifications to export."});let e=(0,u.Pe)(o);(0,u.Bf)(e,"notifications"),g().fire({icon:"success",title:"Export Complete",text:"Exported ".concat(o.length," notifications to CSV file."),timer:2e3,showConfirmButton:!1})},disabled:j,className:"bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:[(0,i.jsx)("i",{className:"fas fa-download mr-2"}),"Export CSV"]}),(0,i.jsxs)("button",{onClick:()=>w(!0),disabled:j,className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:[(0,i.jsx)("i",{className:"fas fa-plus mr-2"}),"Send Notification"]}),(0,i.jsxs)("button",{onClick:A,disabled:j,className:"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:[(0,i.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})]})}),(0,i.jsx)("div",{className:"p-6",children:(0,i.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:0===o.length?(0,i.jsxs)("div",{className:"text-center py-12",children:[(0,i.jsx)("i",{className:"fas fa-bell-slash text-gray-300 text-6xl mb-4"}),(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No notifications sent yet"}),(0,i.jsx)("p",{className:"text-gray-500 mb-4",children:"Start by sending your first notification to users"}),(0,i.jsxs)("button",{onClick:()=>w(!0),className:"bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg",children:[(0,i.jsx)("i",{className:"fas fa-plus mr-2"}),"Send First Notification"]})]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"bg-gray-50 px-6 py-3 border-b border-gray-200",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("label",{className:"flex items-center",children:[(0,i.jsx)("input",{type:"checkbox",checked:D.length===o.length&&o.length>0,onChange:()=>{D.length===o.length?N([]):N(o.map(e=>e.id).filter(Boolean))},className:"mr-3"}),(0,i.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:["Select All (",o.length," notifications)"]})]}),D.length>0&&(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsxs)("span",{className:"text-sm text-gray-600",children:[D.length," selected"]}),(0,i.jsxs)("button",{onClick:T,disabled:j,className:"text-red-600 hover:text-red-800 disabled:opacity-50",children:[(0,i.jsx)("i",{className:"fas fa-trash mr-1"}),"Delete Selected"]})]})]})}),(0,i.jsx)("div",{className:"divide-y divide-gray-200",children:o.map(e=>{var t;return(0,i.jsx)("div",{className:"p-6 hover:bg-gray-50 ".concat(D.includes(e.id)?"bg-blue-50":""),children:(0,i.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,i.jsx)("div",{className:"flex-shrink-0 mt-1",children:(0,i.jsx)("input",{type:"checkbox",checked:D.includes(e.id),onChange:()=>M(e.id),className:"mr-3"})}),(0,i.jsx)("div",{className:"flex-shrink-0 mt-1",children:(0,i.jsx)("i",{className:U(e.type)})}),(0,i.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("h3",{className:"text-lg font-medium text-gray-900 flex items-center",children:[e.title,(0,i.jsx)("span",{className:"ml-2 px-2 py-1 text-xs font-bold bg-red-100 text-red-800 rounded-full",children:"\uD83D\uDEA8 BLOCKING"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat("success"===e.type?"bg-green-100 text-green-800":"warning"===e.type?"bg-yellow-100 text-yellow-800":"error"===e.type?"bg-red-100 text-red-800":"bg-blue-100 text-blue-800"),children:e.type.charAt(0).toUpperCase()+e.type.slice(1)}),(0,i.jsx)("button",{onClick:()=>F(e.id,e.title),disabled:j,className:"text-red-600 hover:text-red-800 disabled:opacity-50 p-1",title:"Delete notification",children:(0,i.jsx)("i",{className:"fas fa-trash"})})]})]}),(0,i.jsx)("p",{className:"text-gray-700 mt-2",children:e.message}),(0,i.jsx)("div",{className:"flex items-center justify-between mt-4",children:(0,i.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,i.jsxs)("span",{children:[(0,i.jsx)("i",{className:"fas fa-user mr-1"}),"By: ",e.createdBy]}),(0,i.jsxs)("span",{children:[(0,i.jsx)("i",{className:"fas fa-users mr-1"}),"Target: ","all"===e.targetUsers?"All Users":"".concat((null==(t=e.userIds)?void 0:t.length)||0," Selected Users")]}),(0,i.jsxs)("span",{children:[(0,i.jsx)("i",{className:"fas fa-clock mr-1"}),k(e.createdAt)]})]})})]})]})},e.id)})})]})})}),y&&(0,i.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,i.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:[(0,i.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,i.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:"Send Notification"}),(0,i.jsx)("button",{onClick:()=>w(!1),className:"text-gray-500 hover:text-gray-700",children:(0,i.jsx)("i",{className:"fas fa-times text-xl"})})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Title"}),(0,i.jsx)("input",{type:"text",value:S.title,onChange:e=>C(t=>({...t,title:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",placeholder:"Enter notification title..."})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Message"}),(0,i.jsx)("textarea",{value:S.message,onChange:e=>C(t=>({...t,message:e.target.value})),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",placeholder:"Enter notification message..."})]}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Type"}),(0,i.jsxs)("select",{value:S.type,onChange:e=>C(t=>({...t,type:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,i.jsx)("option",{value:"info",children:"Info"}),(0,i.jsx)("option",{value:"success",children:"Success"}),(0,i.jsx)("option",{value:"warning",children:"Warning"}),(0,i.jsx)("option",{value:"error",children:"Error"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Target"}),(0,i.jsxs)("select",{value:S.targetUsers,onChange:e=>C(t=>({...t,targetUsers:e.target.value,selectedUserIds:"all"===e.target.value?[]:t.selectedUserIds})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,i.jsx)("option",{value:"all",children:"All Users"}),(0,i.jsx)("option",{value:"specific",children:"Specific Users"})]})]})]}),(0,i.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,i.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,i.jsx)("i",{className:"fas fa-exclamation-triangle text-red-500 mt-1"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:"\uD83D\uDEA8 All Notifications are Blocking (Mandatory)"}),(0,i.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Users must acknowledge this notification before they can continue with any activities (watching videos, accessing dashboard features, etc.)"})]})]})}),"specific"===S.targetUsers&&(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select Users"}),(0,i.jsx)("div",{className:"max-h-40 overflow-y-auto border border-gray-300 rounded-lg p-2",children:f.map(e=>(0,i.jsxs)("label",{className:"flex items-center p-2 hover:bg-gray-50 rounded",children:[(0,i.jsx)("input",{type:"checkbox",checked:S.selectedUserIds.includes(e.id),onChange:t=>{t.target.checked?C(t=>({...t,selectedUserIds:[...t.selectedUserIds,e.id]})):C(t=>({...t,selectedUserIds:t.selectedUserIds.filter(t=>t!==e.id)}))},className:"mr-3"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-medium text-gray-900",children:e.name||"Unknown User"}),(0,i.jsxs)("div",{className:"text-sm text-gray-500",children:[e.email||"No email"," • ",e.plan||"No plan"]})]})]},e.id))}),(0,i.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:[S.selectedUserIds.length," user(s) selected"]})]})]}),(0,i.jsxs)("div",{className:"flex justify-end space-x-4 mt-6",children:[(0,i.jsx)("button",{onClick:()=>w(!1),className:"px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300",children:"Cancel"}),(0,i.jsx)("button",{onClick:I,disabled:b,className:"px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50",children:b?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"spinner w-4 h-4 mr-2 inline-block"}),"Sending..."]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("i",{className:"fas fa-paper-plane mr-2"}),"Send Notification"]})})]})]})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,8818,6874,3592,6681,8441,1684,7358],()=>t(4880)),_N_E=e.O()}]);