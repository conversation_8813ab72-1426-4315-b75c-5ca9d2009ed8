'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRequireAdmin } from '@/hooks/useAuth'
import { downloadCSV, formatWithdrawalsForExport } from '@/lib/csvExport'
import { getUserData, getWalletData } from '@/lib/dataService'
import { optimizedService } from '@/lib/optimizedDataService'
import { formatDisplayDate, safeToDate } from '@/lib/dateUtils'
import Swal from 'sweetalert2'

interface Withdrawal {
  id: string
  userId: string
  userName: string
  userEmail: string
  userMobile: string
  userPlan: string
  userActiveDays: number
  walletBalance: number
  amount: number
  bankDetails: {
    accountHolderName: string
    accountNumber: string
    ifscCode: string
    bankName: string
  }
  requestDate: Date
  status: 'pending' | 'approved' | 'rejected' | 'completed'
  adminNotes?: string
}

// Raw withdrawal data from Firestore
interface RawWithdrawal {
  id: string
  userId?: string
  amount?: number
  bankDetails?: any
  status?: string
  date?: Date
  adminNotes?: string
  [key: string]: any // Allow additional properties
}

export default function AdminWithdrawalsPage() {
  console.log('🔍 AdminWithdrawalsPage component loaded')

  const { user, loading, isAdmin } = useRequireAdmin()
  const [withdrawals, setWithdrawals] = useState<Withdrawal[]>([])
  const [dataLoading, setDataLoading] = useState(true)
  const [filterStatus, setFilterStatus] = useState('pending')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedWithdrawal, setSelectedWithdrawal] = useState<Withdrawal | null>(null)
  const [showDetailsModal, setShowDetailsModal] = useState(false)
  const [selectedWithdrawals, setSelectedWithdrawals] = useState<string[]>([])
  const [selectAll, setSelectAll] = useState(false)
  const [bulkAction, setBulkAction] = useState('')
  const [isBulkProcessing, setIsBulkProcessing] = useState(false)
  const [showAllWithdrawals, setShowAllWithdrawals] = useState(false)

  useEffect(() => {
    console.log('🔍 useEffect triggered - isAdmin:', isAdmin, 'loading:', loading)
    if (isAdmin) {
      console.log('🔍 Admin verified, loading withdrawals...')
      loadWithdrawals()
    }
  }, [isAdmin])

  const loadWithdrawals = async () => {
    try {
      setDataLoading(true)

      // Use optimized admin withdrawals function (reduces 100s of reads to 1 function call)
      try {
        console.log('🚀 Loading withdrawals with optimized function...')
        const withdrawalData = await optimizedService.getAdminWithdrawals(showAllWithdrawals)
        console.log('🔍 Raw withdrawal data from Firebase Functions:', withdrawalData)
        console.log('🔍 First withdrawal date:', withdrawalData[0]?.requestDate, typeof withdrawalData[0]?.requestDate)

        // Ensure dates are properly converted
        const formattedWithdrawals: Withdrawal[] = withdrawalData.map((withdrawal: any) => ({
          id: withdrawal.id,
          userId: withdrawal.userId,
          userName: withdrawal.userName,
          userEmail: withdrawal.userEmail,
          userMobile: withdrawal.userMobile,
          userPlan: withdrawal.userPlan,
          userActiveDays: withdrawal.userActiveDays,
          walletBalance: withdrawal.walletBalance,
          amount: withdrawal.amount,
          bankDetails: withdrawal.bankDetails,
          requestDate: withdrawal.requestDate, // Already converted in Firebase Functions
          status: withdrawal.status,
          adminNotes: withdrawal.adminNotes
        }))

        setWithdrawals(formattedWithdrawals)
        console.log(`✅ Loaded ${formattedWithdrawals.length} withdrawals via optimized function`)

      } catch (optimizedError) {
        console.warn('⚠️ Optimized function failed, using fallback:', optimizedError)

        // Fallback to original method
        const { getAllPendingWithdrawals, getAllWithdrawals } = await import('@/lib/adminDataService')

        let withdrawalData: any[]
        if (showAllWithdrawals) {
          console.log('📋 Loading ALL withdrawals (all statuses)...')
          withdrawalData = await getAllWithdrawals()
        } else {
          console.log('⏳ Loading ALL PENDING withdrawals...')
          withdrawalData = await getAllPendingWithdrawals()
        }

        // For each withdrawal, we need to fetch user data to get name, email, plan, etc.
        const withdrawalsWithUserData: Withdrawal[] = []

        for (const rawWithdrawal of withdrawalData) {
          try {
            // Cast to our expected type
            const withdrawal = rawWithdrawal as RawWithdrawal

            // Type check and ensure withdrawal has required properties
            if (!withdrawal.userId || !withdrawal.amount) {
              console.warn('Withdrawal missing required fields:', withdrawal)
              continue
            }

            const userData = await getUserData(withdrawal.userId)
            const walletData = await getWalletData(withdrawal.userId)

            if (userData) {
              // Use centralized active days calculation
              const { calculateUserActiveDays } = await import('@/lib/dataService')
              const activeDays = await calculateUserActiveDays(withdrawal.userId)

              withdrawalsWithUserData.push({
                id: withdrawal.id,
                userId: withdrawal.userId,
                userName: userData.name,
                userEmail: userData.email,
                userMobile: userData.mobile || '',
                userPlan: userData.plan,
                userActiveDays: activeDays,
                walletBalance: walletData?.wallet || 0,
                amount: withdrawal.amount,
                bankDetails: withdrawal.bankDetails || {
                  accountHolderName: '',
                  accountNumber: '',
                  ifscCode: '',
                  bankName: ''
                },
                requestDate: safeToDate(withdrawal.date),
                status: withdrawal.status as 'pending' | 'approved' | 'rejected' | 'completed' || 'pending',
                adminNotes: withdrawal.adminNotes
              })
            }
          } catch (userError) {
            console.error(`Error loading user data for withdrawal ${rawWithdrawal.id}:`, userError)
            // Skip this withdrawal if user data can't be loaded
          }
        }

        setWithdrawals(withdrawalsWithUserData)
        console.log(`✅ Loaded ${withdrawalsWithUserData.length} withdrawals with fallback method`)
      }

    } catch (error) {
      console.error('Error loading withdrawals:', error)
      setWithdrawals([])
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Failed to load withdrawals. Please try again.',
      })
    } finally {
      setDataLoading(false)
    }
  }

  // Toggle between pending only and all withdrawals
  const toggleWithdrawalView = () => {
    setShowAllWithdrawals(!showAllWithdrawals)
    // Reset filter when toggling
    setFilterStatus(showAllWithdrawals ? 'pending' : '')
    setSelectedWithdrawals([])
    setSelectAll(false)
  }

  // Reload withdrawals when view mode changes
  useEffect(() => {
    if (isAdmin) {
      loadWithdrawals()
    }
  }, [showAllWithdrawals])

  const handleStatusUpdate = async (withdrawalId: string, newStatus: string, notes?: string) => {
    try {
      // Update withdrawal status in Firestore
      const { updateWithdrawalStatus } = await import('@/lib/adminDataService')
      await updateWithdrawalStatus(withdrawalId, newStatus, notes)

      // Update local state
      setWithdrawals(prev => prev.map(w =>
        w.id === withdrawalId
          ? { ...w, status: newStatus as any, adminNotes: notes }
          : w
      ))

      Swal.fire({
        icon: 'success',
        title: 'Status Updated',
        text: `Withdrawal has been ${newStatus}.`,
        timer: 2000,
        showConfirmButton: false
      })
    } catch (error) {
      console.error('Error updating withdrawal status:', error)
      Swal.fire({
        icon: 'error',
        title: 'Update Failed',
        text: 'Failed to update withdrawal status. Please try again.',
      })
    }
  }

  const handleApprove = (withdrawal: Withdrawal) => {
    Swal.fire({
      title: 'Approve Withdrawal',
      text: `Approve withdrawal of ₹${withdrawal.amount} for ${withdrawal.userName}?`,
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#10b981',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Yes, Approve',
      cancelButtonText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        handleStatusUpdate(withdrawal.id, 'approved')
      }
    })
  }

  const handleReject = (withdrawal: Withdrawal) => {
    Swal.fire({
      title: 'Reject Withdrawal',
      text: 'Please provide a reason for rejection:',
      input: 'textarea',
      inputPlaceholder: 'Enter rejection reason...',
      showCancelButton: true,
      confirmButtonColor: '#ef4444',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Reject',
      cancelButtonText: 'Cancel',
      inputValidator: (value) => {
        if (!value) {
          return 'Please provide a reason for rejection'
        }
      }
    }).then((result) => {
      if (result.isConfirmed) {
        handleStatusUpdate(withdrawal.id, 'rejected', result.value)
      }
    })
  }

  const handleComplete = (withdrawal: Withdrawal) => {
    Swal.fire({
      title: 'Mark as Completed',
      text: `Mark withdrawal of ₹${withdrawal.amount} as completed?`,
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#3b82f6',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Yes, Complete',
      cancelButtonText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        handleStatusUpdate(withdrawal.id, 'completed')
      }
    })
  }

  // Bulk selection functions
  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedWithdrawals([])
      setSelectAll(false)
    } else {
      const allIds = filteredWithdrawals.map(w => w.id)
      setSelectedWithdrawals(allIds)
      setSelectAll(true)
    }
  }

  const handleSelectWithdrawal = (withdrawalId: string) => {
    if (selectedWithdrawals.includes(withdrawalId)) {
      setSelectedWithdrawals(prev => prev.filter(id => id !== withdrawalId))
      setSelectAll(false)
    } else {
      setSelectedWithdrawals(prev => [...prev, withdrawalId])
      if (selectedWithdrawals.length + 1 === filteredWithdrawals.length) {
        setSelectAll(true)
      }
    }
  }

  // Bulk status update
  const handleBulkStatusUpdate = async () => {
    if (selectedWithdrawals.length === 0) {
      Swal.fire({
        icon: 'warning',
        title: 'No Selection',
        text: 'Please select at least one withdrawal to update.',
      })
      return
    }

    if (!bulkAction) {
      Swal.fire({
        icon: 'warning',
        title: 'No Action Selected',
        text: 'Please select an action to perform.',
      })
      return
    }

    const actionText = bulkAction === 'approved' ? 'approve' :
                     bulkAction === 'rejected' ? 'reject' :
                     bulkAction === 'completed' ? 'mark as completed' : bulkAction

    let confirmResult

    if (bulkAction === 'rejected') {
      confirmResult = await Swal.fire({
        title: `Bulk ${actionText.charAt(0).toUpperCase() + actionText.slice(1)}`,
        text: 'Please provide a reason for rejection:',
        input: 'textarea',
        inputPlaceholder: 'Enter rejection reason...',
        showCancelButton: true,
        confirmButtonColor: '#ef4444',
        cancelButtonColor: '#6b7280',
        confirmButtonText: `${actionText.charAt(0).toUpperCase() + actionText.slice(1)} ${selectedWithdrawals.length} withdrawals`,
        cancelButtonText: 'Cancel',
        inputValidator: (value) => {
          if (!value) {
            return 'Please provide a reason for rejection'
          }
        }
      })
    } else {
      confirmResult = await Swal.fire({
        title: `Bulk ${actionText.charAt(0).toUpperCase() + actionText.slice(1)}`,
        text: `Are you sure you want to ${actionText} ${selectedWithdrawals.length} selected withdrawals?`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: bulkAction === 'approved' ? '#10b981' : '#3b82f6',
        cancelButtonColor: '#6b7280',
        confirmButtonText: `Yes, ${actionText.charAt(0).toUpperCase() + actionText.slice(1)}`,
        cancelButtonText: 'Cancel'
      })
    }

    if (confirmResult.isConfirmed) {
      try {
        setIsBulkProcessing(true)

        // Update all selected withdrawals
        for (const withdrawalId of selectedWithdrawals) {
          await handleStatusUpdate(withdrawalId, bulkAction, confirmResult.value)
        }

        // Clear selections
        setSelectedWithdrawals([])
        setSelectAll(false)
        setBulkAction('')

        Swal.fire({
          icon: 'success',
          title: 'Bulk Update Complete',
          text: `Successfully ${actionText}ed ${selectedWithdrawals.length} withdrawals.`,
          timer: 3000,
          showConfirmButton: false
        })
      } catch (error) {
        console.error('Error in bulk update:', error)
        Swal.fire({
          icon: 'error',
          title: 'Bulk Update Failed',
          text: 'Some withdrawals could not be updated. Please try again.',
        })
      } finally {
        setIsBulkProcessing(false)
      }
    }
  }

  const filteredWithdrawals = withdrawals.filter(withdrawal => {
    // In pending-only mode, all withdrawals are already pending, so no status filter needed
    // In all-withdrawals mode, apply status filter if selected
    const matchesStatus = showAllWithdrawals ? (!filterStatus || withdrawal.status === filterStatus) : true

    const matchesSearch = !searchTerm ||
      String(withdrawal.userName || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      String(withdrawal.userEmail || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      String(withdrawal.userMobile || '').toLowerCase().includes(searchTerm.toLowerCase())

    return matchesStatus && matchesSearch
  })

  const formatCurrency = (amount: number | undefined) => {
    if (amount === undefined || amount === null || isNaN(amount)) {
      return '₹0.00'
    }
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(amount)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'approved':
        return 'bg-green-100 text-green-800'
      case 'rejected':
        return 'bg-red-100 text-red-800'
      case 'completed':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getPlanColor = (plan: string) => {
    switch (plan.toLowerCase()) {
      case 'trial':
        return 'bg-gray-100 text-gray-800'
      case 'starter':
        return 'bg-green-100 text-green-800'
      case 'premium':
        return 'bg-blue-100 text-blue-800'
      case 'gold':
        return 'bg-yellow-100 text-yellow-800'
      case 'platinum':
        return 'bg-purple-100 text-purple-800'
      case 'diamond':
        return 'bg-pink-100 text-pink-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const handleExportWithdrawals = () => {
    if (filteredWithdrawals.length === 0) {
      Swal.fire({
        icon: 'warning',
        title: 'No Data',
        text: 'No withdrawals to export.',
      })
      return
    }

    const exportData = formatWithdrawalsForExport(filteredWithdrawals)
    downloadCSV(exportData, 'withdrawals')

    Swal.fire({
      icon: 'success',
      title: 'Export Complete',
      text: `Exported ${filteredWithdrawals.length} withdrawals to CSV file.`,
      timer: 2000,
      showConfirmButton: false
    })
  }

  const handleExportPendingWithdrawals = () => {
    const pendingWithdrawals = withdrawals.filter(w => w.status === 'pending')

    if (pendingWithdrawals.length === 0) {
      Swal.fire({
        icon: 'warning',
        title: 'No Pending Withdrawals',
        text: 'No pending withdrawals to export.',
      })
      return
    }

    const exportData = formatWithdrawalsForExport(pendingWithdrawals)
    downloadCSV(exportData, 'pending-withdrawals')

    Swal.fire({
      icon: 'success',
      title: 'Export Complete',
      text: `Exported ${pendingWithdrawals.length} pending withdrawals to CSV file.`,
      timer: 2000,
      showConfirmButton: false
    })
  }

  console.log('🔍 Render check - loading:', loading, 'dataLoading:', dataLoading, 'isAdmin:', isAdmin)

  if (loading) {
    console.log('🔍 Showing auth loading screen')
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="spinner w-12 h-12 mx-auto mb-4"></div>
          <p className="text-gray-600">Checking authentication...</p>
        </div>
      </div>
    )
  }

  if (!isAdmin) {
    console.log('🔍 User is not admin')
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Access denied. Admin privileges required.</p>
        </div>
      </div>
    )
  }

  if (dataLoading) {
    console.log('🔍 Showing data loading screen')
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="spinner w-12 h-12 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading withdrawals...</p>
        </div>
      </div>
    )
  }

  console.log('🔍 Rendering main component')

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center space-x-4">
            <Link
              href="/admin"
              className="text-gray-500 hover:text-gray-700"
            >
              <i className="fas fa-arrow-left text-xl"></i>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Withdrawal Requests</h1>
              <p className="text-sm text-gray-600 mt-1">
                {showAllWithdrawals ? 'Viewing all withdrawals' : 'Viewing pending withdrawals only'}
                - No pagination, all data loaded
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <span className="text-gray-700">
              Total: {filteredWithdrawals.length}
              {selectedWithdrawals.length > 0 && (
                <span className="ml-2 text-blue-600 font-medium">
                  ({selectedWithdrawals.length} selected)
                </span>
              )}
            </span>
            <button
              onClick={handleExportPendingWithdrawals}
              className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg"
            >
              <i className="fas fa-clock mr-2"></i>
              Export Pending
            </button>
            <button
              onClick={handleExportWithdrawals}
              className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg"
            >
              <i className="fas fa-download mr-2"></i>
              Export All
            </button>
            <button
              onClick={toggleWithdrawalView}
              className={`px-4 py-2 rounded-lg ${
                showAllWithdrawals
                  ? 'bg-orange-500 hover:bg-orange-600 text-white'
                  : 'bg-blue-500 hover:bg-blue-600 text-white'
              }`}
            >
              <i className={`fas ${showAllWithdrawals ? 'fa-filter' : 'fa-list'} mr-2`}></i>
              {showAllWithdrawals ? 'Show Pending Only' : 'Show All Withdrawals'}
            </button>
            <button
              onClick={() => loadWithdrawals()}
              className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg"
            >
              <i className="fas fa-sync-alt mr-2"></i>
              Refresh
            </button>
          </div>
        </div>
      </header>

      {/* Filters */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className={`grid gap-4 ${showAllWithdrawals ? 'grid-cols-1 md:grid-cols-3' : 'grid-cols-1 md:grid-cols-2'}`}>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Search</label>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search user name, email, or mobile..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {showAllWithdrawals && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Status Filter</label>
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Status</option>
                <option value="pending">Pending</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
                <option value="completed">Completed</option>
              </select>
            </div>
          )}

          <div className="flex items-end">
            <button
              onClick={() => {
                setFilterStatus(showAllWithdrawals ? '' : 'pending')
                setSearchTerm('')
              }}
              className="w-full bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg"
            >
              <i className="fas fa-eraser mr-2"></i>
              Clear Filters
            </button>
          </div>
        </div>

        {!showAllWithdrawals && (
          <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center">
              <i className="fas fa-info-circle text-yellow-600 mr-2"></i>
              <span className="text-sm text-yellow-800">
                Currently showing <strong>pending withdrawals only</strong>.
                Use the "Show All Withdrawals" button above to view all statuses.
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Bulk Operations */}
      {selectedWithdrawals.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mx-6 mb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <span className="text-blue-800 font-medium">
                <i className="fas fa-check-square mr-2"></i>
                {selectedWithdrawals.length} withdrawal{selectedWithdrawals.length > 1 ? 's' : ''} selected
              </span>
              <select
                value={bulkAction}
                onChange={(e) => setBulkAction(e.target.value)}
                className="px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select Action</option>
                <option value="approved">Approve Selected</option>
                <option value="rejected">Reject Selected</option>
                <option value="completed">Mark as Completed</option>
              </select>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={handleBulkStatusUpdate}
                disabled={!bulkAction || isBulkProcessing}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg disabled:opacity-50"
              >
                {isBulkProcessing ? (
                  <>
                    <div className="spinner w-4 h-4 mr-2 inline-block"></div>
                    Processing...
                  </>
                ) : (
                  <>
                    <i className="fas fa-bolt mr-2"></i>
                    Apply Action
                  </>
                )}
              </button>
              <button
                onClick={() => {
                  setSelectedWithdrawals([])
                  setSelectAll(false)
                  setBulkAction('')
                }}
                className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg"
              >
                <i className="fas fa-times mr-2"></i>
                Clear Selection
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Withdrawals Table */}
      <div className="p-6">
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={selectAll}
                        onChange={handleSelectAll}
                        className="mr-2"
                      />
                      Select All
                    </div>
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    User
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Mobile
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Plan
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Active Days
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Wallet Balance
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Account Holder
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Bank Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Account Number
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    IFSC Code
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Request Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredWithdrawals.map((withdrawal) => (
                  <tr key={withdrawal.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedWithdrawals.includes(withdrawal.id)}
                        onChange={() => handleSelectWithdrawal(withdrawal.id)}
                        className="rounded"
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {withdrawal.userName}
                        </div>
                        <div className="text-sm text-gray-500">
                          {withdrawal.userEmail}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm text-gray-900">
                        {withdrawal.userMobile || 'N/A'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPlanColor(withdrawal.userPlan)}`}>
                        {withdrawal.userPlan}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm font-medium text-gray-900">
                        {withdrawal.userActiveDays} days
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm font-medium text-gray-900">
                        {formatCurrency(withdrawal.walletBalance)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-lg font-bold text-green-600">
                        {formatCurrency(withdrawal.amount)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm text-gray-900">
                        {withdrawal.bankDetails.accountHolderName || 'N/A'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm text-gray-900">
                        {withdrawal.bankDetails.bankName || 'N/A'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm text-gray-900 font-mono">
                        {withdrawal.bankDetails.accountNumber || 'N/A'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm text-gray-900 font-mono">
                        {withdrawal.bankDetails.ifscCode || 'N/A'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDisplayDate(withdrawal.requestDate)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(withdrawal.status)}`}>
                        {withdrawal.status ? withdrawal.status.charAt(0).toUpperCase() + withdrawal.status.slice(1) : 'Unknown'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                      <button
                        onClick={() => {
                          setSelectedWithdrawal(withdrawal)
                          setShowDetailsModal(true)
                        }}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        View
                      </button>
                      {withdrawal.status === 'pending' && (
                        <>
                          <button
                            onClick={() => handleApprove(withdrawal)}
                            className="text-green-600 hover:text-green-900"
                          >
                            Approve
                          </button>
                          <button
                            onClick={() => handleReject(withdrawal)}
                            className="text-red-600 hover:text-red-900"
                          >
                            Reject
                          </button>
                        </>
                      )}
                      {withdrawal.status === 'approved' && (
                        <button
                          onClick={() => handleComplete(withdrawal)}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          Complete
                        </button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Results Info */}
          <div className="bg-white px-4 py-3 border-t border-gray-200 text-center text-sm text-gray-600">
            <div className="flex items-center justify-center space-x-4">
              <span>
                Showing {withdrawals.length} {showAllWithdrawals ? 'total' : 'pending'} withdrawals
              </span>
              {withdrawals.length > 0 && (
                <span className="text-green-600 font-medium">
                  <i className="fas fa-check-circle mr-1"></i>
                  All loaded in single page
                </span>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Details Modal */}
      {showDetailsModal && selectedWithdrawal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-bold">Withdrawal Details</h3>
              <button
                onClick={() => setShowDetailsModal(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <i className="fas fa-times"></i>
              </button>
            </div>
            
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700">User</label>
                <p className="text-sm text-gray-900">{selectedWithdrawal.userName}</p>
                <p className="text-sm text-gray-500">{selectedWithdrawal.userEmail}</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Mobile Number</label>
                <p className="text-sm text-gray-900">{selectedWithdrawal.userMobile || 'N/A'}</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Plan</label>
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPlanColor(selectedWithdrawal.userPlan)}`}>
                  {selectedWithdrawal.userPlan}
                </span>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Active Days</label>
                <p className="text-sm font-medium text-gray-900">{selectedWithdrawal.userActiveDays} days</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Wallet Balance</label>
                <p className="text-sm font-medium text-gray-900">{formatCurrency(selectedWithdrawal.walletBalance)}</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Withdrawal Amount</label>
                <p className="text-lg font-bold text-green-600">{formatCurrency(selectedWithdrawal.amount)}</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700">Bank Details</label>
                <div className="text-sm text-gray-900">
                  <p><strong>Account Holder:</strong> {selectedWithdrawal.bankDetails.accountHolderName}</p>
                  <p><strong>Bank:</strong> {selectedWithdrawal.bankDetails.bankName}</p>
                  <p><strong>Account Number:</strong> {selectedWithdrawal.bankDetails.accountNumber}</p>
                  <p><strong>IFSC Code:</strong> {selectedWithdrawal.bankDetails.ifscCode}</p>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700">Status</label>
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(selectedWithdrawal.status)}`}>
                  {selectedWithdrawal.status ? selectedWithdrawal.status.charAt(0).toUpperCase() + selectedWithdrawal.status.slice(1) : 'Unknown'}
                </span>
              </div>
              
              {selectedWithdrawal.adminNotes && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Admin Notes</label>
                  <p className="text-sm text-gray-900">{selectedWithdrawal.adminNotes}</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
