"use strict";exports.id=4887,exports.ids=[4887,7087],exports.modules={744:(e,t,a)=>{a.d(t,{J:()=>r});var o=a(43210),n=a(3582);function r(e){let[t,a]=(0,o.useState)(!1),[r,s]=(0,o.useState)(!0);return{hasBlockingNotifications:t,isChecking:r,checkForBlockingNotifications:async()=>{try{s(!0);let t=await (0,n.iA)(e);a(t)}catch(e){console.error("Error checking for blocking notifications:",e),a(!1)}finally{s(!1)}},markAllAsRead:()=>{a(!1)}}}},55986:(e,t,a)=>{a.d(t,{l:()=>r});var o=a(43210),n=a(87087);function r({userId:e,checkInterval:t=3e4,enabled:a=!0}){let[r,s]=(0,o.useState)({blocked:!1,lastChecked:new Date}),[i,c]=(0,o.useState)(!1);return{leaveStatus:r,isChecking:i,checkLeaveStatus:(0,o.useCallback)(async()=>{if(e&&a)try{c(!0);let t=await (0,n.q8)(e);return s({blocked:t.blocked,reason:t.reason,lastChecked:new Date}),t}catch(e){return console.error("Error checking leave status:",e),s(e=>({...e,lastChecked:new Date})),{blocked:!1}}finally{c(!1)}},[e,a]),isBlocked:r.blocked}}},87087:(e,t,a)=>{a.d(t,{applyUserLeave:()=>u,cancelUserLeave:()=>f,createAdminLeave:()=>s,debugAdminLeaveStatus:()=>c,deleteAdminLeave:()=>l,getAdminLeaves:()=>i,getUserLeaves:()=>m,getUserMonthlyLeaveCount:()=>g,isAdminLeaveDay:()=>d,isUserOnLeave:()=>h,q8:()=>w});var o=a(33784),n=a(75535);let r={adminLeaves:"adminLeaves",userLeaves:"userLeaves"};async function s(e){try{return(await (0,n.gS)((0,n.collection)(o.db,r.adminLeaves),{...e,date:n.Dc.fromDate(e.date),createdAt:n.Dc.now()})).id}catch(e){throw console.error("Error creating admin leave:",e),e}}async function i(){try{let e=(0,n.P)((0,n.collection)(o.db,r.adminLeaves),(0,n.My)("date","asc")),t=(await (0,n.getDocs)(e)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate(),createdAt:e.data().createdAt.toDate()}));return console.log("\uD83D\uDCC5 All admin leaves:",t),t}catch(e){throw console.error("Error getting admin leaves:",e),e}}async function c(){try{let e=new Date;console.log("\uD83D\uDD0D Debug: Checking admin leave status for today:",e.toDateString());let t=await d(e);console.log("\uD83D\uDCCA Debug: Admin leave result:",t);let a=await i();console.log("\uD83D\uDCC5 Debug: All admin leaves in database:",a);let o=a.filter(t=>t.date.toDateString()===e.toDateString());console.log("\uD83D\uDCC5 Debug: Today's admin leaves:",o)}catch(e){console.error("❌ Debug: Error checking admin leave status:",e)}}async function l(e){try{await (0,n.kd)((0,n.H9)(o.db,r.adminLeaves,e))}catch(e){throw console.error("Error deleting admin leave:",e),e}}async function d(e){try{let t=new Date(e);t.setHours(0,0,0,0);let a=new Date(e);a.setHours(23,59,59,999),console.log("\uD83D\uDD0D Checking admin leave for date range:",t.toISOString(),"to",a.toISOString());let s=(0,n.P)((0,n.collection)(o.db,r.adminLeaves),(0,n._M)("date",">=",n.Dc.fromDate(t)),(0,n._M)("date","<=",n.Dc.fromDate(a))),i=await (0,n.getDocs)(s),c=!i.empty;return c?console.log("\uD83D\uDCC5 Found admin leave(s) for today:",i.docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate()}))):console.log("\uD83D\uDCC5 No admin leaves found for today"),c}catch(e){return console.error("❌ Error checking admin leave day:",e),!1}}async function u(e){try{let t,a,s,i=new Date,c=i.getFullYear(),l=i.getMonth()+1,d=await g(e.userId,c,l),u="pending";return d<4&&(u="approved",t="system",s=n.Dc.now(),a=`Auto-approved: ${d+1}/4 monthly leaves used`),{id:(await (0,n.gS)((0,n.collection)(o.db,r.userLeaves),{...e,date:n.Dc.fromDate(e.date),status:u,appliedAt:n.Dc.now(),...t&&{reviewedBy:t},...s&&{reviewedAt:s},...a&&{reviewNotes:a}})).id,autoApproved:"approved"===u,usedLeaves:d+ +("approved"===u),maxLeaves:4}}catch(e){throw console.error("Error applying user leave:",e),e}}async function m(e){try{let t=(0,n.P)((0,n.collection)(o.db,r.userLeaves),(0,n._M)("userId","==",e),(0,n.My)("date","desc"));return(await (0,n.getDocs)(t)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate(),appliedAt:e.data().appliedAt.toDate(),reviewedAt:e.data().reviewedAt?.toDate()}))}catch(e){throw console.error("Error getting user leaves:",e),e}}async function f(e){try{await (0,n.kd)((0,n.H9)(o.db,r.userLeaves,e))}catch(e){throw console.error("Error cancelling user leave:",e),e}}async function g(e,t,a){try{let s=new Date(t,a-1,1),i=new Date(t,a,0,23,59,59,999),c=(0,n.P)((0,n.collection)(o.db,r.userLeaves),(0,n._M)("userId","==",e),(0,n._M)("status","==","approved"),(0,n._M)("date",">=",n.Dc.fromDate(s)),(0,n._M)("date","<=",n.Dc.fromDate(i)));return(await (0,n.getDocs)(c)).size}catch(e){return console.error("Error getting user monthly leave count:",e),0}}async function h(e,t){try{let a=new Date(t);a.setHours(0,0,0,0);let s=new Date(t);s.setHours(23,59,59,999),console.log("\uD83D\uDD0D Checking user leave for user:",e,"on date range:",a.toISOString(),"to",s.toISOString());let i=(0,n.P)((0,n.collection)(o.db,r.userLeaves),(0,n._M)("userId","==",e),(0,n._M)("status","==","approved"),(0,n._M)("date",">=",n.Dc.fromDate(a)),(0,n._M)("date","<=",n.Dc.fromDate(s))),c=await (0,n.getDocs)(i),l=!c.empty;return l?console.log("\uD83D\uDC64 Found user leave(s) for today:",c.docs.map(e=>({id:e.id,...e.data(),date:e.data().date.toDate()}))):console.log("\uD83D\uDC64 No user leaves found for today"),l}catch(e){return console.error("❌ Error checking user leave day:",e),!1}}async function w(e){try{let t=new Date;console.log("\uD83D\uDD0D Checking work block status for user:",e,"on date:",t.toDateString());try{let e=await d(t);if(console.log("\uD83D\uDCC5 Admin leave check result:",e),e)return console.log("\uD83D\uDEAB Work blocked due to admin leave"),{blocked:!0,reason:"System maintenance/holiday"}}catch(e){console.error("❌ Error checking admin leave (allowing work to continue):",e)}try{let a=await h(e,t);if(console.log("\uD83D\uDC64 User leave check result:",a),a)return console.log("\uD83D\uDEAB Work blocked due to user leave"),{blocked:!0,reason:"You are on approved leave today"}}catch(e){console.error("❌ Error checking user leave (allowing work to continue):",e)}return console.log("✅ Work is not blocked"),{blocked:!1}}catch(e){return console.error("❌ Error checking work block status (allowing work to continue):",e),{blocked:!1}}}},92617:(e,t,a)=>{a.d(t,{x8:()=>v});var o=a(24791),n=a(33784);let r=(0,o.Qg)(n.Cn,"getUserDashboardData"),s=(0,o.Qg)(n.Cn,"submitVideoBatch"),i=(0,o.Qg)(n.Cn,"processWithdrawalRequest"),c=(0,o.Qg)(n.Cn,"getUserNotifications"),l=(0,o.Qg)(n.Cn,"getUserTransactions"),d=(0,o.Qg)(n.Cn,"getAdminWithdrawals"),u=(0,o.Qg)(n.Cn,"getAdminDashboardStats"),m=(0,o.Qg)(n.Cn,"getAdminUsers"),f=(0,o.Qg)(n.Cn,"getAdminNotifications"),g=(0,o.Qg)(n.Cn,"createAdminNotification"),h=new Map;async function w(e){try{console.log("\uD83D\uDE80 Using optimized dashboard data function for user:",e),console.log("\uD83D\uDD17 Functions instance:",n.Cn.app.options.projectId);let t=new Promise((e,t)=>{setTimeout(()=>t(Error("Function timeout")),5e3)}),a=await Promise.race([r({userId:e}),t]);if(console.log("\uD83D\uDCE1 Function response received:",a),a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success){console.log("✅ Dashboard data loaded via optimized function");let t=e.data;return{userData:{name:t.user.name,email:t.user.email,mobile:t.user.mobile,referralCode:t.user.referralCode,plan:t.user.plan,planExpiry:null,activeDays:t.user.activeDays},walletData:{wallet:t.user.wallet},videoData:{totalVideos:t.videos.total,todayVideos:t.videos.today,remainingVideos:t.videos.remaining}}}throw console.error("❌ Function returned success: false",e),Error("Function returned success: false")}throw console.error("❌ Invalid function response structure:",a),Error("Invalid response from dashboard function")}catch(e){throw console.error("❌ Error in optimized dashboard data:",e),console.error("❌ Error details:",{name:e?.name,message:e?.message,code:e?.code,details:e?.details}),e}}async function p(){try{console.log("\uD83D\uDE80 Using optimized admin dashboard stats function...");let e=await u({});if(e.data&&"object"==typeof e.data&&"success"in e.data){let t=e.data;if(t.success)return console.log("✅ Admin dashboard stats loaded via optimized function"),t.data}throw Error("Invalid response from admin dashboard stats function")}catch(e){throw console.error("❌ Error in optimized admin dashboard stats:",e),e}}let v={getDashboardData:async function(e){let t=`dashboard-${e}`,o=Date.now(),n=h.get(t);if(n&&o-n.timestamp<n.ttl)return console.log("\uD83C\uDFAF Using cached fallback dashboard data"),n.data;try{let a=await w(e);return h.set(t,{data:a,timestamp:o,ttl:6e4}),a}catch(f){console.warn("⚠️ Optimized function failed, falling back to direct calls:",f instanceof Error?f.message:String(f));let{getUserData:n,getWalletData:r,getVideoCountData:s}=await a.e(3582).then(a.bind(a,3582));console.log("\uD83D\uDCCA Loading dashboard data via direct Firestore calls...");let i=Date.now(),[c,l,d]=await Promise.all([n(e),r(e),s(e)]),u=Date.now()-i;console.log(`✅ Dashboard data loaded via fallback in ${u}ms`);let m={userData:c,walletData:l,videoData:d};return h.set(t,{data:m,timestamp:o,ttl:3e4}),m}},submitVideoBatch:async function(e,t=50){try{console.log("\uD83D\uDE80 Using optimized video batch submission...");let a=await s({userId:e,videoCount:t});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Video batch submitted via optimized function"),e.data}throw Error("Invalid response from video batch function")}catch(e){throw console.error("❌ Error in optimized video batch submission:",e),e}},processWithdrawal:async function(e){try{console.log("\uD83D\uDE80 Using optimized withdrawal processing...");let t=await i(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Withdrawal processed via optimized function"),e.data}throw Error("Invalid response from withdrawal function")}catch(e){throw console.error("❌ Error in optimized withdrawal processing:",e),e}},getUserNotifications:async function(e,t=10){try{console.log("\uD83D\uDE80 Using optimized notifications function...");let a=await c({userId:e,limit:t});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Notifications loaded via optimized function"),e.data}throw Error("Invalid response from notifications function")}catch(e){throw console.error("❌ Error in optimized notifications:",e),e}},getUserTransactions:async function(e,t=10,a="all"){try{console.log("\uD83D\uDE80 Using optimized transactions function...");let o=await l({userId:e,limit:t,type:a});if(o.data&&"object"==typeof o.data&&"success"in o.data){let e=o.data;if(e.success)return console.log("✅ Transactions loaded via optimized function"),e.data}throw Error("Invalid response from transactions function")}catch(e){throw console.error("❌ Error in optimized transactions:",e),e}},getAdminWithdrawals:async function(e=!1){try{console.log("\uD83D\uDE80 Using optimized admin withdrawals function, showAll:",e),console.log("\uD83D\uDD17 Functions instance:",n.Cn.app.options.projectId);let t=await d({showAllWithdrawals:e});if(console.log("\uD83D\uDCE1 Admin withdrawals function response received:",t),t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin withdrawals loaded via optimized function"),e.data;throw console.error("❌ Admin withdrawals function returned success: false",e),Error("Admin withdrawals function returned success: false")}throw console.error("❌ Invalid admin withdrawals function response structure:",t),Error("Invalid response from admin withdrawals function")}catch(e){throw console.error("❌ Error in optimized admin withdrawals:",e),console.error("❌ Error details:",{name:e?.name,message:e?.message,code:e?.code,details:e?.details}),e}},getAdminDashboardStats:async function(){try{return await p()}catch(t){console.warn("⚠️ Optimized admin stats function failed, falling back to direct calls");let{getAdminDashboardStats:e}=await Promise.all([a.e(3582),a.e(1391)]).then(a.bind(a,91391));return await e()}},getAdminUsers:async function(e={}){try{console.log("\uD83D\uDE80 Using optimized admin users function...");let t=await m(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin users loaded via optimized function"),e.data}throw Error("Invalid response from admin users function")}catch(e){throw console.error("❌ Error in optimized admin users:",e),e}},getAdminNotifications:async function(e=50,t="all"){try{console.log("\uD83D\uDE80 Using optimized admin notifications function...");let a=await f({limit:e,type:t});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Admin notifications loaded via optimized function"),e.data}throw Error("Invalid response from admin notifications function")}catch(e){throw console.error("❌ Error in optimized admin notifications:",e),e}},createAdminNotification:async function(e){try{console.log("\uD83D\uDE80 Using optimized admin notification creation...");let t=await g(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin notification created via optimized function"),e.data}throw Error("Invalid response from admin notification creation function")}catch(e){throw console.error("❌ Error in optimized admin notification creation:",e),e}},areFunctionsAvailable:async function(){try{console.log("\uD83D\uDD0D Testing Firebase Functions connectivity..."),console.log("\uD83D\uDD17 Functions project:",n.Cn.app.options.projectId),console.log("\uD83D\uDD17 Functions region:",n.Cn.region);let e=await r({userId:"test"});return console.log("✅ Functions are available, test response:",e),!0}catch(e){return console.warn("⚠️ Firebase Functions not available, falling back to direct Firestore"),console.error("❌ Functions test error:",{name:e?.name,message:e?.message,code:e?.code,details:e?.details}),!1}}}},98873:(e,t,a)=>{a.d(t,{A:()=>s});var o=a(60687),n=a(43210),r=a(3582);function s({userId:e,onAllRead:t}){let[a,s]=(0,n.useState)([]),[i,c]=(0,n.useState)(0),[l,d]=(0,n.useState)(!0),u=async()=>{let o=a[i];o?.id&&(await (0,r.bA)(o.id,e),i<a.length-1?c(i+1):t())};if(l)return(0,o.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",children:(0,o.jsx)("div",{className:"bg-white rounded-lg p-8 max-w-md w-full mx-4",children:(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("div",{className:"spinner w-8 h-8 mx-auto mb-4"}),(0,o.jsx)("p",{className:"text-gray-600",children:"Loading notifications..."})]})})});if(0===a.length)return null;let m=a[i];return(0,o.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",children:(0,o.jsxs)("div",{className:"bg-white rounded-lg shadow-2xl max-w-md w-full mx-4 overflow-hidden",children:[(0,o.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6",children:(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,o.jsx)("i",{className:(e=>{switch(e){case"success":return"fas fa-check-circle text-green-500";case"warning":return"fas fa-exclamation-triangle text-yellow-500";case"error":return"fas fa-times-circle text-red-500";default:return"fas fa-info-circle text-blue-500"}})(m.type)}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-lg font-bold",children:"Important Notice"}),(0,o.jsxs)("p",{className:"text-blue-100 text-sm",children:[i+1," of ",a.length," notifications"]})]})]}),(0,o.jsx)("div",{className:"bg-white bg-opacity-20 rounded-full px-3 py-1",children:(0,o.jsx)("span",{className:"text-sm font-medium",children:"Required"})})]})}),(0,o.jsxs)("div",{className:"p-6",children:[(0,o.jsx)("h4",{className:"text-xl font-bold text-gray-900 mb-3",children:m.title}),(0,o.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 mb-4",children:(0,o.jsx)("p",{className:"text-gray-800 leading-relaxed",children:m.message})}),(0,o.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-6",children:[(0,o.jsxs)("span",{children:["From: ",m.createdBy]}),(0,o.jsx)("span",{children:(e=>{let t=Math.floor((new Date().getTime()-e.getTime())/1e3);return t<60?"Just now":t<3600?`${Math.floor(t/60)} minutes ago`:t<86400?`${Math.floor(t/3600)} hours ago`:`${Math.floor(t/86400)} days ago`})(m.createdAt)})]}),(0,o.jsxs)("div",{className:"mb-6",children:[(0,o.jsxs)("div",{className:"flex justify-between text-sm text-gray-600 mb-2",children:[(0,o.jsx)("span",{children:"Progress"}),(0,o.jsxs)("span",{children:[i+1,"/",a.length]})]}),(0,o.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,o.jsx)("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:{width:`${(i+1)/a.length*100}%`}})})]}),(0,o.jsxs)("button",{onClick:u,className:"w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2",children:[(0,o.jsx)("i",{className:"fas fa-check"}),(0,o.jsx)("span",{children:i<a.length-1?"Acknowledge & Continue":"Acknowledge & Proceed"})]})]}),(0,o.jsx)("div",{className:"bg-gray-50 px-6 py-4 border-t",children:(0,o.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-sm text-gray-600",children:[(0,o.jsx)("i",{className:"fas fa-info-circle"}),(0,o.jsx)("span",{children:"You must acknowledge all notifications to continue"})]})})]})})}}};