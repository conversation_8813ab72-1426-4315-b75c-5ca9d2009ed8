import * as functions from 'firebase-functions/v1';
import * as admin from 'firebase-admin';

// Initialize Firebase Admin
admin.initializeApp();
const db = admin.firestore();

// Collection and field constants
const COLLECTIONS = {
  users: 'users',
  transactions: 'transactions',
  withdrawals: 'withdrawals',
  notifications: 'notifications',
  adminLeaves: 'adminLeaves',
  userLeaves: 'userLeaves'
};

const FIELD_NAMES = {
  totalVideos: 'totalVideos',
  todayVideos: 'todayVideos',
  lastVideoDate: 'lastVideoDate',
  activeDays: 'activeDays',
  wallet: 'wallet',
  plan: 'plan',
  planExpiry: 'planExpiry',
  joinedDate: 'joinedDate',
  quickVideoAdvantageRemainingDays: 'quickVideoAdvantageRemainingDays',
  manuallySetActiveDays: 'manuallySetActiveDays',
  lastActiveDaysUpdate: 'lastActiveDaysUpdate',
  userId: 'userId',
  referralCode: 'referralCode',
  referredBy: 'referredBy'
};

// Get plan-based earning amount (per batch of 1000 videos)
function getPlanEarning(plan: string): number {
  const planEarnings: { [key: string]: number } = {
    'Trial': 10,
    'Starter': 25,
    'Basic': 75,
    'Premium': 150,
    'Gold': 200,
    'Platinum': 250,
    'Diamond': 400
  };

  return planEarnings[plan] || 10; // Default to trial earning (per batch of 1000 videos)
}

// Helper function to check if date is admin leave day
async function isAdminLeaveDay(date: Date): Promise<boolean> {
  const dateString = date.toDateString();
  const adminLeavesSnapshot = await db.collection(COLLECTIONS.adminLeaves)
    .where('date', '==', dateString)
    .limit(1)
    .get();
  return !adminLeavesSnapshot.empty;
}

// Helper function to check if date is user leave day
async function isUserLeaveDay(userId: string, date: Date): Promise<boolean> {
  const dateString = date.toDateString();
  const userLeavesSnapshot = await db.collection(COLLECTIONS.userLeaves)
    .where('userId', '==', userId)
    .where('date', '==', dateString)
    .where('status', '==', 'approved')
    .limit(1)
    .get();
  return !userLeavesSnapshot.empty;
}



// 🎯 OPTIMIZED FUNCTION: Get User Dashboard Data
// Replaces multiple client-side reads with single function call
export const getUserDashboardData = functions
  .runWith({ memory: '256MB', timeoutSeconds: 60 })
  .https.onCall(async (data, context) => {
  try {
    // Verify authentication
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const userId = context.auth.uid;
    console.log(`📊 Getting dashboard data for user: ${userId}`);

    // Single read to get user data
    const userDoc = await db.collection(COLLECTIONS.users).doc(userId).get();
    if (!userDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'User not found');
    }

    const userData = userDoc.data()!;
    const today = new Date();
    
    // Check leave days (batch read)
    const isAdminLeave = await isAdminLeaveDay(today);
    const isUserLeave = await isUserLeaveDay(userId, today);
    
    // Calculate plan expiry and days left
    const activeDays = userData[FIELD_NAMES.activeDays] || 1;

    let expired = false;
    let daysLeft = 0;

    if (userData[FIELD_NAMES.plan] === 'Trial') {
      expired = activeDays >= 3; // Expires from day 3 and above
      daysLeft = Math.max(0, 3 - activeDays);
    } else {
      expired = activeDays >= 31; // Expires from active days 31 and above
      daysLeft = Math.max(0, 31 - activeDays);
    }

    // Return comprehensive dashboard data
    return {
      success: true,
      data: {
        user: {
          name: userData.name,
          email: userData.email,
          mobile: userData.mobile || '',
          referralCode: userData[FIELD_NAMES.referralCode] || '',
          plan: userData[FIELD_NAMES.plan],
          wallet: userData[FIELD_NAMES.wallet] || 0,
          activeDays: activeDays,
          daysLeft: daysLeft,
          expired: expired
        },
        videos: {
          total: userData[FIELD_NAMES.totalVideos] || 0,
          today: userData[FIELD_NAMES.todayVideos] || 0,
          remaining: Math.max(0, 1000 - (userData[FIELD_NAMES.todayVideos] || 0))
        },
        status: {
          canWork: !expired && !isAdminLeave && !isUserLeave,
          isAdminLeave: isAdminLeave,
          isUserLeave: isUserLeave
        },
        quickVideo: {
          remainingDays: userData[FIELD_NAMES.quickVideoAdvantageRemainingDays] || 0,
          active: (userData[FIELD_NAMES.quickVideoAdvantageRemainingDays] || 0) > 0
        }
      }
    };
  } catch (error) {
    console.error('Error in getUserDashboardData:', error);
    throw new functions.https.HttpsError('internal', 'Failed to get dashboard data');
  }
});

// 🎯 OPTIMIZED FUNCTION: Submit Video Batch
// Replaces multiple reads/writes with single atomic operation
export const submitVideoBatch = functions
  .runWith({ memory: '256MB', timeoutSeconds: 60 })
  .https.onCall(async (data, context) => {
  try {
    // Verify authentication
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const userId = context.auth.uid;
    const { videosWatched = 1000 } = data;

    console.log(`🎬 Processing video batch submission for user: ${userId}`);

    // Use transaction for atomic operation
    const result = await db.runTransaction(async (transaction) => {
      const userRef = db.collection(COLLECTIONS.users).doc(userId);
      const userDoc = await transaction.get(userRef);
      
      if (!userDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'User not found');
      }

      const userData = userDoc.data()!;
      const today = new Date();
      const todayString = today.toDateString();
      
      // Check if user can submit videos
      const lastVideoDate = userData[FIELD_NAMES.lastVideoDate]?.toDate();
      const isNewDay = !lastVideoDate || lastVideoDate.toDateString() !== todayString;
      
      const currentTodayVideos = isNewDay ? 0 : (userData[FIELD_NAMES.todayVideos] || 0);
      const currentTotalVideos = userData[FIELD_NAMES.totalVideos] || 0;
      
      // Validate submission
      if (currentTodayVideos >= 1000) {
        throw new functions.https.HttpsError('failed-precondition', 'Daily video limit already reached');
      }

      // Calculate new values
      const newTodayVideos = Math.min(currentTodayVideos + videosWatched, 1000);
      const newTotalVideos = currentTotalVideos + videosWatched;

      // Calculate plan-based earnings (per batch of 1000 videos)
      const userPlan = userData[FIELD_NAMES.plan] || 'Trial';
      const planEarnings = getPlanEarning(userPlan);

      // Calculate earnings based on videos watched (proportional to plan earnings)
      const earnings = Math.round((videosWatched / 1000) * planEarnings);
      const newWallet = (userData[FIELD_NAMES.wallet] || 0) + earnings;

      // Update user data atomically
      transaction.update(userRef, {
        [FIELD_NAMES.totalVideos]: newTotalVideos,
        [FIELD_NAMES.todayVideos]: newTodayVideos,
        [FIELD_NAMES.lastVideoDate]: admin.firestore.Timestamp.fromDate(today),
        [FIELD_NAMES.wallet]: newWallet
      });

      // Add transaction record
      const transactionRef = db.collection(COLLECTIONS.transactions).doc();
      transaction.set(transactionRef, {
        [FIELD_NAMES.userId]: userId,
        type: 'video_earning',
        amount: earnings,
        description: `Earnings for ${videosWatched} videos`,
        date: admin.firestore.Timestamp.fromDate(today),
        status: 'completed'
      });

      return {
        videosAdded: videosWatched,
        totalVideos: newTotalVideos,
        todayVideos: newTodayVideos,
        earnings: earnings,
        newWallet: newWallet
      };
    });

    console.log(`✅ Video batch submitted successfully for user ${userId}:`, result);
    return { success: true, data: result };

  } catch (error) {
    console.error('Error in submitVideoBatch:', error);
    if (error instanceof functions.https.HttpsError) {
      throw error;
    }
    throw new functions.https.HttpsError('internal', 'Failed to submit video batch');
  }
});

// 🎯 SCHEDULED FUNCTION: Daily Active Days Increment
// Runs automatically every day at midnight IST, no client involvement
export const dailyActiveDaysIncrement = functions
  .runWith({
    timeoutSeconds: 540, // 9 minutes timeout
    memory: '1GB'
  })
  .pubsub
  .schedule('0 0 * * *')
  .timeZone('Asia/Kolkata')
  .onRun(async (_context) => {
    try {
      console.log('🌅 Starting scheduled daily active days increment...');
      console.log('⏰ Execution time:', new Date().toISOString());
      console.log('🌍 Timezone: Asia/Kolkata');

      const today = new Date();
      const todayString = today.toDateString();
      console.log('📅 Processing date:', todayString);

      // Use shared daily increment logic
      const result = await runDailyIncrementProcess();

      // Update tracking to show it was triggered by scheduled function
      await db.collection('system').doc('dailyReset').update({
        triggeredBy: 'scheduled_function'
      });

      console.log(`✅ Scheduled daily increment completed:`, result);
      return result;
      
    } catch (error) {
      console.error('Error in scheduled daily increment:', error);
      return { success: false, error: (error as Error).message };
    }
  });

// 🎯 OPTIMIZED FUNCTION: Process Withdrawal Request
// Handles withdrawal with atomic transaction and validation
export const processWithdrawalRequest = functions
  .runWith({ memory: '256MB', timeoutSeconds: 60 })
  .https.onCall(async (data, context) => {
  try {
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const userId = context.auth.uid;
    const { amount } = data;

    console.log(`💰 Processing withdrawal request for user ${userId}: ₹${amount}`);

    // Validate amount
    if (!amount || amount < 50) {
      throw new functions.https.HttpsError('invalid-argument', 'Minimum withdrawal amount is ₹50');
    }

    // Use transaction for atomic operation
    const result = await db.runTransaction(async (transaction) => {
      const userRef = db.collection(COLLECTIONS.users).doc(userId);
      const userDoc = await transaction.get(userRef);

      if (!userDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'User not found');
      }

      const userData = userDoc.data()!;
      const currentWallet = userData[FIELD_NAMES.wallet] || 0;

      // Check sufficient balance
      if (currentWallet < amount) {
        throw new functions.https.HttpsError('failed-precondition', 'Insufficient wallet balance');
      }

      // Check for pending withdrawals
      const pendingWithdrawals = await db.collection(COLLECTIONS.withdrawals)
        .where('userId', '==', userId)
        .where('status', '==', 'pending')
        .limit(1)
        .get();

      if (!pendingWithdrawals.empty) {
        throw new functions.https.HttpsError('failed-precondition', 'You have a pending withdrawal request');
      }

      // Check time restrictions (10 AM to 6 PM)
      const now = new Date();
      const hour = now.getHours();
      if (hour < 10 || hour >= 18) {
        throw new functions.https.HttpsError('failed-precondition', 'Withdrawals are only allowed between 10 AM to 6 PM');
      }

      // Check leave days
      const isAdminLeave = await isAdminLeaveDay(now);
      const isUserLeave = await isUserLeaveDay(userId, now);

      if (isAdminLeave || isUserLeave) {
        throw new functions.https.HttpsError('failed-precondition', 'Withdrawals are not allowed on leave days');
      }

      // Deduct from wallet
      const newWallet = currentWallet - amount;
      transaction.update(userRef, {
        [FIELD_NAMES.wallet]: newWallet
      });

      // Create withdrawal request
      const withdrawalRef = db.collection(COLLECTIONS.withdrawals).doc();
      transaction.set(withdrawalRef, {
        userId: userId,
        amount: amount,
        status: 'pending',
        date: admin.firestore.Timestamp.now(),
        userEmail: userData.email,
        userName: userData.name
      });

      // Add transaction record
      const transactionRef = db.collection(COLLECTIONS.transactions).doc();
      transaction.set(transactionRef, {
        [FIELD_NAMES.userId]: userId,
        type: 'withdrawal_request',
        amount: -amount,
        description: `Withdrawal request of ₹${amount}`,
        date: admin.firestore.Timestamp.now(),
        status: 'pending'
      });

      return {
        withdrawalId: withdrawalRef.id,
        amount: amount,
        newWallet: newWallet,
        status: 'pending'
      };
    });

    console.log(`✅ Withdrawal request processed for user ${userId}:`, result);
    return { success: true, data: result };

  } catch (error) {
    console.error('Error in processWithdrawalRequest:', error);
    if (error instanceof functions.https.HttpsError) {
      throw error;
    }
    throw new functions.https.HttpsError('internal', 'Failed to process withdrawal request');
  }
});

// 🎯 OPTIMIZED FUNCTION: Get User Notifications
// Combines notification queries and reduces reads
export const getUserNotifications = functions
  .runWith({ memory: '256MB', timeoutSeconds: 30 })
  .https.onCall(async (data, context) => {
  try {
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const userId = context.auth.uid;
    const { limit = 10 } = data;

    console.log(`📢 Getting notifications for user: ${userId}`);

    // Get notifications in parallel
    const [allUsersSnapshot, specificUserSnapshot] = await Promise.all([
      db.collection(COLLECTIONS.notifications)
        .where('targetUsers', '==', 'all')
        .orderBy('createdAt', 'desc')
        .limit(limit)
        .get(),
      db.collection(COLLECTIONS.notifications)
        .where('targetUsers', '==', 'specific')
        .where('userIds', 'array-contains', userId)
        .orderBy('createdAt', 'desc')
        .limit(limit)
        .get()
    ]);

    // Combine and sort notifications
    const allNotifications = [
      ...allUsersSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })),
      ...specificUserSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }))
    ];

    // Sort by creation date
    allNotifications.sort((a: any, b: any) => {
      const aTime = a.createdAt?.toMillis() || 0;
      const bTime = b.createdAt?.toMillis() || 0;
      return bTime - aTime;
    });

    // Limit results
    const notifications = allNotifications.slice(0, limit);

    console.log(`📢 Found ${notifications.length} notifications for user ${userId}`);
    return { success: true, data: notifications };

  } catch (error) {
    console.error('Error in getUserNotifications:', error);
    throw new functions.https.HttpsError('internal', 'Failed to get notifications');
  }
});

// 🎯 OPTIMIZED FUNCTION: Get User Transactions
// Optimized transaction history with pagination
export const getUserTransactions = functions
  .runWith({ memory: '256MB', timeoutSeconds: 30 })
  .https.onCall(async (data, context) => {
  try {
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const userId = context.auth.uid;
    const { limit = 10, type = 'all' } = data;

    console.log(`💳 Getting transactions for user: ${userId}, type: ${type}`);

    let query = db.collection(COLLECTIONS.transactions)
      .where(FIELD_NAMES.userId, '==', userId);

    if (type !== 'all') {
      query = query.where('type', '==', type);
    }

    const snapshot = await query
      .orderBy('date', 'desc')
      .limit(limit)
      .get();

    const transactions = snapshot.docs.map(doc => {
      const data = doc.data();

      // Debug: Log the actual Firestore data
      console.log(`🔍 Transaction ${doc.id} date:`, data.date, typeof data.date);

      return {
        id: doc.id,
        ...data,
        date: data.date
      };
    });

    console.log(`💳 Found ${transactions.length} transactions for user ${userId}`);
    return { success: true, data: transactions };

  } catch (error) {
    console.error('Error in getUserTransactions:', error);
    throw new functions.https.HttpsError('internal', 'Failed to get transactions');
  }
});

// 🎯 OPTIMIZED FUNCTION: Get Admin Withdrawals with User Data
// Combines withdrawal data with user information in single function call
export const getAdminWithdrawals = functions
  .runWith({ memory: '512MB', timeoutSeconds: 120 })
  .https.onCall(async (data, context) => {
  try {
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    // Verify admin access (check if user email is admin)
    const adminEmails = ['<EMAIL>', '<EMAIL>'];
    if (!adminEmails.includes(context.auth.token.email || '')) {
      throw new functions.https.HttpsError('permission-denied', 'Admin access required');
    }

    const { showAllWithdrawals = false } = data;

    console.log(`🏦 Loading withdrawals for admin (showAll: ${showAllWithdrawals})`);

    // Get withdrawals based on filter
    let withdrawalsQuery;
    if (showAllWithdrawals) {
      withdrawalsQuery = db.collection(COLLECTIONS.withdrawals)
        .orderBy('date', 'desc');
    } else {
      withdrawalsQuery = db.collection(COLLECTIONS.withdrawals)
        .where('status', '==', 'pending')
        .orderBy('date', 'desc');
    }

    const withdrawalsSnapshot = await withdrawalsQuery.get();
    console.log(`🏦 Found ${withdrawalsSnapshot.docs.length} withdrawals`);

    // Get all unique user IDs
    const allUserIds = withdrawalsSnapshot.docs.map(doc => doc.data().userId).filter(Boolean);
    const uniqueUserIds = new Set(allUserIds);
    const userIds: string[] = [];
    uniqueUserIds.forEach(id => userIds.push(id));
    console.log(`👥 Need to fetch data for ${userIds.length} unique users`);

    // Batch fetch all user data
    const userDataMap = new Map();
    const userBatches = [];
    for (let i = 0; i < userIds.length; i += 10) {
      userBatches.push(userIds.slice(i, i + 10));
    }

    for (const batch of userBatches) {
      const userPromises = batch.map(async (userId) => {
        try {
          const userDoc = await db.collection(COLLECTIONS.users).doc(userId).get();
          if (userDoc.exists) {
            const userData = userDoc.data();

            // Calculate active days (simplified for performance)
            const activeDays = await calculateActiveDaysForUser(userId, userData);

            return {
              userId,
              userData: {
                ...userData,
                activeDays
              }
            };
          }
          return null;
        } catch (error) {
          console.error(`Error fetching user ${userId}:`, error);
          return null;
        }
      });

      const batchResults = await Promise.all(userPromises);
      batchResults.forEach(result => {
        if (result) {
          userDataMap.set(result.userId, result.userData);
        }
      });
    }

    // Combine withdrawal data with user data
    const enrichedWithdrawals = withdrawalsSnapshot.docs.map(doc => {
      const withdrawalData = doc.data();
      const userData = userDataMap.get(withdrawalData.userId);

      return {
        id: doc.id,
        userId: withdrawalData.userId,
        userName: userData?.name || 'Unknown User',
        userEmail: userData?.email || '',
        userMobile: userData?.mobile || '',
        userPlan: userData?.plan || 'Unknown',
        userActiveDays: userData?.activeDays || 0,
        walletBalance: userData?.wallet || 0,
        amount: withdrawalData.amount || 0,
        bankDetails: withdrawalData.bankDetails || {
          accountHolderName: '',
          accountNumber: '',
          ifscCode: '',
          bankName: ''
        },
        requestDate: withdrawalData.date,
        status: withdrawalData.status || 'pending',
        adminNotes: withdrawalData.adminNotes || ''
      };
    });

    console.log(`✅ Successfully enriched ${enrichedWithdrawals.length} withdrawals with user data`);
    return { success: true, data: enrichedWithdrawals };

  } catch (error) {
    console.error('Error in getAdminWithdrawals:', error);
    throw new functions.https.HttpsError('internal', 'Failed to get admin withdrawals');
  }
});

// Helper function to get plan earning per batch (for pricing plans)
function getPricingPlanEarning(plan: string): number {
  const planEarnings: { [key: string]: number } = {
    'Trial': 0,
    'Rs 499': 10,
    'Rs 1499': 75,
    'Rs 2999': 60,
    'Rs 3999': 80,
    'Rs 5999': 120,
    'Rs 9999': 200
  };
  return planEarnings[plan] || 0;
}

// Core daily increment logic (shared between scheduled function and backup)
async function runDailyIncrementProcess(): Promise<any> {
  const today = new Date();
  const todayString = today.toDateString();

  // Check if today is admin leave day
  const isAdminLeave = await isAdminLeaveDay(today);
  if (isAdminLeave) {
    console.log('⏸️ Skipping increment - Admin leave day');
    return { success: true, reason: 'Admin leave day', incrementedCount: 0 };
  }

  let incrementedCount = 0;
  let skippedCount = 0;
  let errorCount = 0;
  let lastDoc: any = null;

  // Process users in batches
  do {
    let query = db.collection(COLLECTIONS.users).limit(100);
    if (lastDoc) {
      query = query.startAfter(lastDoc);
    }

    const snapshot = await query.get();
    if (snapshot.empty) break;

    const batch = db.batch();

    for (const userDoc of snapshot.docs) {
      try {
        const userData = userDoc.data();
        const userId = userDoc.id;

        // Skip if manually set active days
        if (userData[FIELD_NAMES.manuallySetActiveDays]) {
          skippedCount++;
          continue;
        }

        // Check if user is on leave today
        const isUserLeave = await isUserLeaveDay(userId, today);
        if (isUserLeave) {
          skippedCount++;
          continue;
        }

        // Increment active days
        const currentActiveDays = userData[FIELD_NAMES.activeDays] || 1;
        const newActiveDays = currentActiveDays + 1;

        batch.update(userDoc.ref, {
          [FIELD_NAMES.activeDays]: newActiveDays,
          [FIELD_NAMES.lastActiveDaysUpdate]: admin.firestore.Timestamp.fromDate(today)
        });

        incrementedCount++;

      } catch (error) {
        console.error(`Error processing user ${userDoc.id}:`, error);
        errorCount++;
      }
    }

    // Commit batch
    await batch.commit();
    lastDoc = snapshot.docs[snapshot.docs.length - 1];

  } while (lastDoc);

  // Update system tracking
  await db.collection('system').doc('dailyReset').set({
    lastResetDate: todayString,
    lastResetTimestamp: admin.firestore.Timestamp.now(),
    incrementedCount,
    skippedCount,
    errorCount,
    triggeredBy: 'backup_mechanism'
  }, { merge: true });

  return {
    success: true,
    incrementedCount,
    skippedCount,
    errorCount
  };
}



// Helper function to get active days for a user (uses stored value from database)
async function calculateActiveDaysForUser(userId: string, userData: any): Promise<number> {
  try {
    if (!userData) return 1;

    // CRITICAL FIX: Always use the stored activeDays value from the database
    // The daily increment system manages the correct active days values
    // Recalculating based on dates bypasses the stored values and causes incorrect results
    const storedActiveDays = userData[FIELD_NAMES.activeDays] || 1;

    console.log(`📅 Using stored active days for user ${userId}: ${storedActiveDays} (plan: ${userData.plan || 'Unknown'})`);

    return storedActiveDays;
  } catch (error) {
    console.error('Error getting user active days:', error);
    return 1; // Return 1 on error to ensure minimum value
  }
}



// 🎯 OPTIMIZED FUNCTION: Get Admin Dashboard Stats
// Combines multiple collection queries into single function call
export const getAdminDashboardStats = functions
  .runWith({ memory: '256MB', timeoutSeconds: 60 })
  .https.onCall(async (_data, context) => {
  try {
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    // Verify admin access
    const adminEmails = ['<EMAIL>', '<EMAIL>'];
    if (!adminEmails.includes(context.auth.token.email || '')) {
      throw new functions.https.HttpsError('permission-denied', 'Admin access required');
    }

    console.log('📊 Loading admin dashboard stats...');

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Batch queries for better performance (optimized to reduce full collection scans)
    const [
      usersSnapshot,
      todayUsersSnapshot,
      todayTransactionsSnapshot,
      pendingWithdrawalsSnapshot,
      todayWithdrawalsSnapshot
    ] = await Promise.all([
      // Total users - limit to recent users for performance, use count aggregation if available
      db.collection(COLLECTIONS.users).limit(10000).get(),

      // Today's new users
      db.collection(COLLECTIONS.users)
        .where(FIELD_NAMES.joinedDate, '>=', admin.firestore.Timestamp.fromDate(today))
        .get(),

      // Today's transactions only (more efficient than all transactions)
      db.collection(COLLECTIONS.transactions)
        .where('date', '>=', admin.firestore.Timestamp.fromDate(today))
        .get(),

      // Pending withdrawals
      db.collection(COLLECTIONS.withdrawals)
        .where('status', '==', 'pending')
        .get(),

      // Today's withdrawals
      db.collection(COLLECTIONS.withdrawals)
        .where('date', '>=', admin.firestore.Timestamp.fromDate(today))
        .get()
    ]);

    // Calculate totals
    const totalUsers = usersSnapshot.size;
    const todayUsers = todayUsersSnapshot.size;

    // Calculate video and earning totals (optimized approach)
    let totalVideos = 0;
    let totalEarnings = 0;
    let todayVideos = 0;
    let todayEarnings = 0;

    // Get totals from user documents (more efficient than transaction aggregation)
    usersSnapshot.docs.forEach(doc => {
      const userData = doc.data();
      totalVideos += userData[FIELD_NAMES.totalVideos] || 0;
      // Estimate total earnings based on videos and plan
      const userPlan = userData[FIELD_NAMES.plan] || 'Trial';
      const userVideos = userData[FIELD_NAMES.totalVideos] || 0;
      const batches = Math.floor(userVideos / 1000);
      totalEarnings += batches * getPricingPlanEarning(userPlan);
    });

    // Process today's transactions for today's stats
    todayTransactionsSnapshot.docs.forEach(doc => {
      const transaction = doc.data();
      if (transaction.type === 'video_earning') {
        todayVideos += 1000; // Each transaction represents 1000 videos
        todayEarnings += transaction.amount || 0;
      }
    });

    const stats = {
      totalUsers: totalUsers >= 10000 ? `${totalUsers}+` : totalUsers, // Indicate if limited
      totalVideos,
      totalEarnings,
      pendingWithdrawals: pendingWithdrawalsSnapshot.size,
      todayUsers,
      todayVideos,
      todayEarnings,
      todayWithdrawals: todayWithdrawalsSnapshot.size
    };

    console.log('✅ Admin dashboard stats loaded:', stats);
    return { success: true, data: stats };

  } catch (error) {
    console.error('Error in getAdminDashboardStats:', error);
    throw new functions.https.HttpsError('internal', 'Failed to get admin dashboard stats');
  }
});

// 🎯 OPTIMIZED FUNCTION: Get Admin Users with Pagination
// Optimized user loading with server-side filtering and pagination
export const getAdminUsers = functions
  .runWith({ memory: '512MB', timeoutSeconds: 120 })
  .https.onCall(async (data, context) => {
  try {
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    // Verify admin access
    const adminEmails = ['<EMAIL>', '<EMAIL>'];
    if (!adminEmails.includes(context.auth.token.email || '')) {
      throw new functions.https.HttpsError('permission-denied', 'Admin access required');
    }

    const {
      page = 1,
      limit = 20,
      searchTerm = '',
      planFilter = '',
      sortBy = 'joinedDate',
      sortOrder = 'desc'
    } = data;

    console.log(`👥 Loading admin users (page: ${page}, limit: ${limit}, search: "${searchTerm}")`);

    // Build query step by step
    let usersQuery: admin.firestore.Query = db.collection(COLLECTIONS.users);

    // Apply plan filter if specified
    if (planFilter && planFilter !== 'all') {
      usersQuery = usersQuery.where(FIELD_NAMES.plan, '==', planFilter);
    }

    // Apply sorting
    usersQuery = usersQuery.orderBy(sortBy, sortOrder);

    // Apply pagination
    const offset = (page - 1) * limit;
    if (offset > 0) {
      // For pagination, we need to get the starting document
      const offsetQuery = db.collection(COLLECTIONS.users)
        .orderBy(sortBy, sortOrder)
        .limit(offset);
      const offsetSnapshot = await offsetQuery.get();
      if (!offsetSnapshot.empty) {
        const lastDoc = offsetSnapshot.docs[offsetSnapshot.docs.length - 1];
        usersQuery = usersQuery.startAfter(lastDoc);
      }
    }

    usersQuery = usersQuery.limit(limit);

    const snapshot = await usersQuery.get();
    console.log(`👥 Found ${snapshot.docs.length} users`);

    // Process users and calculate active days
    const users = await Promise.all(snapshot.docs.map(async (doc) => {
      const userData = doc.data();

      // Calculate active days
      const activeDays = await calculateActiveDaysForUser(doc.id, userData);

      // Calculate referrals count (excluding trial plans)
      const referralsQuery = db.collection(COLLECTIONS.users)
        .where(FIELD_NAMES.referredBy, '==', userData.referralCode || '');
      const referralsSnapshot = await referralsQuery.get();
      const referralsCount = referralsSnapshot.size;

      // Count referrals excluding trial plans
      const referralsDoneExcludingTrial = referralsSnapshot.docs.filter(refDoc => {
        const refData = refDoc.data();
        return refData.plan !== 'Trial';
      }).length;

      // Handle joined date properly
      let joinedDate = new Date();
      try {
        if (userData.joinedDate) {
          joinedDate = userData.joinedDate.toDate ? userData.joinedDate.toDate() : new Date(userData.joinedDate);
        }
      } catch (error) {
        console.error(`Error processing joined date for user ${doc.id}:`, error);
        joinedDate = new Date();
      }

      return {
        id: doc.id,
        name: userData.name || '',
        email: userData.email || '',
        mobile: userData.mobile || '',
        referralCode: userData.referralCode || '',
        referredBy: userData.referredBy || '',
        plan: userData.plan || 'Trial',
        planExpiry: userData.planExpiry?.toDate() || null,
        activeDays,
        joinedDate,
        wallet: userData.wallet || 0,
        totalVideos: userData.totalVideos || 0,
        todayVideos: userData.todayVideos || 0,
        videoDuration: userData.videoDuration || 30,
        quickVideoAdvantageRemainingDays: userData.quickVideoAdvantageRemainingDays || 0,
        referralsCount,
        referralsDoneExcludingTrial,
        status: userData.status || 'active'
      };
    }));

    // Apply search filter on processed data (for name, email, mobile)
    let filteredUsers = users;
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filteredUsers = users.filter(user =>
        user.name.toLowerCase().includes(searchLower) ||
        user.email.toLowerCase().includes(searchLower) ||
        user.mobile.includes(searchTerm) ||
        user.referralCode.toLowerCase().includes(searchLower)
      );
    }

    // Get total count for pagination (approximate)
    const totalQuery = db.collection(COLLECTIONS.users);
    const totalSnapshot = await totalQuery.get();
    const totalUsers = totalSnapshot.size;

    console.log(`✅ Processed ${filteredUsers.length} users for admin`);
    return {
      success: true,
      data: {
        users: filteredUsers,
        totalUsers,
        currentPage: page,
        totalPages: Math.ceil(totalUsers / limit),
        hasMore: snapshot.docs.length === limit
      }
    };

  } catch (error) {
    console.error('Error in getAdminUsers:', error);
    throw new functions.https.HttpsError('internal', 'Failed to get admin users');
  }
});

// 🎯 OPTIMIZED FUNCTION: Get Admin Notifications
// Optimized notification management for admin
export const getAdminNotifications = functions
  .runWith({ memory: '256MB', timeoutSeconds: 60 })
  .https.onCall(async (data, context) => {
  try {
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    // Verify admin access
    const adminEmails = ['<EMAIL>', '<EMAIL>'];
    if (!adminEmails.includes(context.auth.token.email || '')) {
      throw new functions.https.HttpsError('permission-denied', 'Admin access required');
    }

    const { limit = 50, type = 'all' } = data;

    console.log(`📢 Loading admin notifications (limit: ${limit}, type: ${type})`);

    let notificationsQuery: admin.firestore.Query = db.collection(COLLECTIONS.notifications)
      .orderBy('createdAt', 'desc');

    if (type !== 'all') {
      notificationsQuery = notificationsQuery.where('type', '==', type);
    }

    if (limit > 0) {
      notificationsQuery = notificationsQuery.limit(limit);
    }

    const snapshot = await notificationsQuery.get();

    const notifications = snapshot.docs.map(doc => ({
      id: doc.id,
      title: doc.data().title || '',
      message: doc.data().message || '',
      type: doc.data().type || 'info',
      targetUsers: doc.data().targetUsers || 'all',
      userIds: doc.data().userIds || [],
      createdAt: doc.data().createdAt?.toDate() || new Date(),
      createdBy: doc.data().createdBy || ''
    }));

    console.log(`✅ Loaded ${notifications.length} notifications for admin`);
    return { success: true, data: notifications };

  } catch (error) {
    console.error('Error in getAdminNotifications:', error);
    throw new functions.https.HttpsError('internal', 'Failed to get admin notifications');
  }
});

// 🎯 OPTIMIZED FUNCTION: Create Admin Notification
// Optimized notification creation with validation
export const createAdminNotification = functions
  .runWith({ memory: '256MB', timeoutSeconds: 60 })
  .https.onCall(async (data, context) => {
  try {
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    // Verify admin access
    const adminEmails = ['<EMAIL>', '<EMAIL>'];
    if (!adminEmails.includes(context.auth.token.email || '')) {
      throw new functions.https.HttpsError('permission-denied', 'Admin access required');
    }

    const { title, message, type, targetUsers, userIds } = data;

    // Validate required fields
    if (!title || !message || !type) {
      throw new functions.https.HttpsError('invalid-argument', 'Title, message, and type are required');
    }

    console.log(`📢 Creating notification: "${title}" for ${targetUsers}`);

    const notificationData = {
      title,
      message,
      type,
      targetUsers: targetUsers || 'all',
      userIds: userIds || [],
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      createdBy: context.auth.token.email || 'admin'
    };

    const docRef = await db.collection(COLLECTIONS.notifications).add(notificationData);

    console.log(`✅ Notification created with ID: ${docRef.id}`);
    return { success: true, data: { id: docRef.id, ...notificationData } };

  } catch (error) {
    console.error('Error in createAdminNotification:', error);
    throw new functions.https.HttpsError('internal', 'Failed to create notification');
  }
});
