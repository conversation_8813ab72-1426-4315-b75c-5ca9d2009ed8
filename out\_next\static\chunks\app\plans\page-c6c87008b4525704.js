(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[481],{4871:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>u});var s=a(5155),i=a(2115),r=a(6874),n=a.n(r),o=a(6681),l=a(4752),c=a.n(l);let d=[{id:"trial",name:"Trial",price:0,duration:2,earningPerVideo:10,videoDuration:30,features:["2 days access","₹10 per batch (1000 videos)","Basic support","Video duration: 30 seconds"]},{id:"starter",name:"Starter",price:499,duration:30,earningPerVideo:25,videoDuration:30,features:["30 days access","₹25 per batch (1000 videos)","Priority support","Referral bonus: ₹50","Video duration: 30 seconds"]},{id:"basic",name:"Basic",price:1499,duration:30,earningPerVideo:75,videoDuration:30,features:["30 days access","₹75 per batch (1000 videos)","Priority support","Referral bonus: ₹150","1000 videos credited to total count","Video duration: 30 seconds"],popular:!0},{id:"premium",name:"Premium",price:2999,duration:30,earningPerVideo:150,videoDuration:30,features:["30 days access","₹150 per batch (1000 videos)","Premium support","Referral bonus: ₹300","1000 videos credited to total count","Video duration: 30 seconds"]},{id:"gold",name:"Gold",price:3999,duration:30,earningPerVideo:200,videoDuration:30,features:["30 days access","₹200 per batch (1000 videos)","Premium support","Referral bonus: ₹400","1000 videos credited to total count","Video duration: 30 seconds","Priority customer support"]},{id:"platinum",name:"Platinum",price:5999,duration:30,earningPerVideo:250,videoDuration:30,features:["30 days access","₹250 per batch (1000 videos)","Premium support","Referral bonus: ₹700","1000 videos credited to total count","Video duration: 30 seconds","Dedicated account manager","Early access to new features"]},{id:"diamond",name:"Diamond",price:9999,duration:30,earningPerVideo:400,videoDuration:30,features:["30 days access","₹400 per batch (1000 videos)","VIP support","Referral bonus: ₹1200","1000 videos credited to total count","Video duration: 30 seconds","Dedicated account manager","Early access to new features","Exclusive earning opportunities"]}];function u(){let{user:e,loading:t}=(0,o.hD)(),[a,r]=(0,i.useState)(null),[l,u]=(0,i.useState)(!1),m=async t=>{if(!e)return void c().fire({icon:"info",title:"Login Required",text:"Please login to purchase a plan",showCancelButton:!0,confirmButtonText:"Login",cancelButtonText:"Cancel"}).then(e=>{e.isConfirmed&&(window.location.href="/login")});if("trial"===t.id)return void c().fire({icon:"info",title:"Trial Plan",text:"You are already on the trial plan. Upgrade to a paid plan for better earnings!"});r(t.id),u(!0);try{await new Promise(e=>setTimeout(e,2e3)),c().fire({icon:"info",title:"Payment Integration Required",html:"\n          <p>To complete your purchase of the <strong>".concat(t.name,"</strong> plan (₹").concat(t.price,'), please contact our support team.</p>\n          <br>\n          <p><strong>Plan Details:</strong></p>\n          <ul style="text-align: left; margin: 10px 0;">\n            <li>Duration: ').concat(t.duration," days</li>\n            <li>Earning: ₹").concat(t.earningPerVideo," per 1000 videos</li>\n            <li>Video duration: ").concat(t.videoDuration<60?"".concat(t.videoDuration," seconds"):"".concat(Math.floor(t.videoDuration/60)," minute").concat(t.videoDuration>=120?"s":""),"</li>\n          </ul>\n          <br>\n          <p><strong>Contact Options:</strong></p>\n          <p>\uD83D\uDCE7 Email: <strong><EMAIL></strong></p>\n        "),confirmButtonText:"Contact Support",showCancelButton:!0,cancelButtonText:"Cancel"})}catch(e){console.error("Error processing plan selection:",e),c().fire({icon:"error",title:"Error",text:"Failed to process plan selection. Please try again."})}finally{u(!1),r(null)}};return t?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"spinner"})}):(0,s.jsxs)("div",{className:"min-h-screen p-4",children:[(0,s.jsx)("header",{className:"glass-card p-4 mb-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)(n(),{href:e?"/dashboard":"/",className:"glass-button px-4 py-2 text-white",children:[(0,s.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back"]}),(0,s.jsx)("h1",{className:"text-xl font-bold text-white",children:"Choose Your Plan"}),(0,s.jsx)("div",{className:"w-20"})," "]})}),(0,s.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[(0,s.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-white mb-4",children:"Start Earning with MyTube"}),(0,s.jsx)("p",{className:"text-white/80 text-lg max-w-2xl mx-auto",children:"Choose the perfect plan for your earning goals. Watch videos and earn money with our flexible pricing options."})]}),(0,s.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-6",children:d.map(e=>(0,s.jsxs)("div",{className:"glass-card p-8 relative ".concat(e.popular?"ring-2 ring-yellow-400":""),children:[e.popular&&(0,s.jsx)("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2",children:(0,s.jsx)("span",{className:"bg-yellow-400 text-black px-4 py-1 rounded-full text-sm font-bold",children:"Most Popular"})}),(0,s.jsxs)("div",{className:"text-center mb-6",children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:e.name}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsxs)("span",{className:"text-4xl font-bold text-white",children:["₹",e.price]}),e.price>0&&(0,s.jsxs)("span",{className:"text-white/60 ml-2",children:["/ ",e.duration," days"]})]}),(0,s.jsxs)("p",{className:"text-green-400 font-semibold",children:["Earn ₹",e.earningPerVideo," per 1000 videos"]})]}),(0,s.jsx)("ul",{className:"space-y-3 mb-8",children:e.features.map((e,t)=>(0,s.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,s.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),e]},t))}),(0,s.jsx)("button",{onClick:()=>m(e),disabled:l&&a===e.id,className:"w-full py-3 rounded-lg font-semibold transition-all duration-300 ".concat(e.popular?"bg-yellow-400 text-black hover:bg-yellow-500":0===e.price?"bg-gray-600 text-white hover:bg-gray-700":"bg-youtube-red text-white hover:bg-red-700"," disabled:opacity-50"),children:l&&a===e.id?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-5 h-5 inline-block"}),"Processing..."]}):0===e.price?"Current Plan":"Choose ".concat(e.name)})]},e.id))}),(0,s.jsxs)("div",{className:"mt-12 glass-card p-8",children:[(0,s.jsxs)("h3",{className:"text-xl font-bold text-white mb-4 text-center",children:[(0,s.jsx)("i",{className:"fas fa-info-circle mr-2"}),"Plan Benefits Explained"]}),(0,s.jsxs)("div",{className:"grid md:grid-cols-2 gap-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-3",children:"Earning Structure"}),(0,s.jsxs)("ul",{className:"space-y-2 text-white/80",children:[(0,s.jsx)("li",{children:"• Watch 1000 videos daily to earn the full amount"}),(0,s.jsx)("li",{children:"• Each video must be watched for the full duration"}),(0,s.jsx)("li",{children:"• Earnings are credited to your earning wallet"}),(0,s.jsx)("li",{children:"• Transfer earnings to main wallet for withdrawal"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-3",children:"Video Duration Benefits"}),(0,s.jsxs)("ul",{className:"space-y-2 text-white/80",children:[(0,s.jsx)("li",{children:"• All plans now have 30-second video duration"}),(0,s.jsx)("li",{children:"• Consistent video duration across all plans"}),(0,s.jsx)("li",{children:"• 1000 videos per day for maximum earnings"}),(0,s.jsx)("li",{children:"• All videos must be watched completely"})]})]})]})]}),(0,s.jsxs)("div",{className:"mt-8 text-center",children:[(0,s.jsx)("p",{className:"text-white/60 mb-4",children:"Need help choosing a plan? Contact us during business hours (9 AM - 6 PM, working days):"}),(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsxs)("a",{href:"mailto:<EMAIL>",className:"flex items-center text-white hover:text-blue-400 transition-colors",children:[(0,s.jsx)("i",{className:"fas fa-envelope mr-2"}),"<EMAIL>"]})})]})]})]})}},9403:(e,t,a)=>{Promise.resolve().then(a.bind(a,4871))}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,8818,6874,6681,8441,1684,7358],()=>t(9403)),_N_E=e.O()}]);