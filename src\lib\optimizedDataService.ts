import { httpsCallable } from 'firebase/functions'
import { functions } from './firebase'

// 🎯 OPTIMIZED DATA SERVICE
// Uses Firebase Functions to reduce Firestore reads by 60-75%

// Initialize Firebase Functions
const getUserDashboardDataFunction = httpsCallable(functions, 'getUserDashboardData')
const submitVideoBatchFunction = httpsCallable(functions, 'submitVideoBatch')
const processWithdrawalRequestFunction = httpsCallable(functions, 'processWithdrawalRequest')
const getUserNotificationsFunction = httpsCallable(functions, 'getUserNotifications')
const getUserTransactionsFunction = httpsCallable(functions, 'getUserTransactions')
const getAdminWithdrawalsFunction = httpsCallable(functions, 'getAdminWithdrawals')
const getAdminDashboardStatsFunction = httpsCallable(functions, 'getAdminDashboardStats')
const getAdminUsersFunction = httpsCallable(functions, 'getAdminUsers')
const getAdminNotificationsFunction = httpsCallable(functions, 'getAdminNotifications')
const createAdminNotificationFunction = httpsCallable(functions, 'createAdminNotification')

// Simple cache for fallback data
interface CacheEntry {
  data: any
  timestamp: number
  ttl: number
}

const fallbackCache = new Map<string, CacheEntry>()

// Dashboard Data - Replaces multiple client-side reads
export async function getOptimizedDashboardData(userId: string) {
  try {
    console.log('🚀 Using optimized dashboard data function for user:', userId)
    console.log('🔗 Functions instance:', functions.app.options.projectId)

    // Add timeout to fail fast if functions are not working
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Function timeout')), 5000) // 5 second timeout
    })

    const result = await Promise.race([
      getUserDashboardDataFunction({ userId }),
      timeoutPromise
    ])
    console.log('📡 Function response received:', result)

    if (result.data && typeof result.data === 'object' && 'success' in result.data) {
      const data = result.data as { success: boolean; data: any }
      if (data.success) {
        console.log('✅ Dashboard data loaded via optimized function')

        // Map the Firebase Function response to the expected frontend format
        const functionData = data.data
        return {
          userData: {
            name: functionData.user.name,
            email: functionData.user.email,
            mobile: functionData.user.mobile,
            referralCode: functionData.user.referralCode,
            plan: functionData.user.plan,
            planExpiry: null, // Not returned by function
            activeDays: functionData.user.activeDays
          },
          walletData: {
            wallet: functionData.user.wallet
          },
          videoData: {
            totalVideos: functionData.videos.total,
            todayVideos: functionData.videos.today,
            remainingVideos: functionData.videos.remaining
          }
        }
      } else {
        console.error('❌ Function returned success: false', data)
        throw new Error('Function returned success: false')
      }
    }

    console.error('❌ Invalid function response structure:', result)
    throw new Error('Invalid response from dashboard function')
  } catch (error: any) {
    console.error('❌ Error in optimized dashboard data:', error)
    console.error('❌ Error details:', {
      name: error?.name,
      message: error?.message,
      code: error?.code,
      details: error?.details
    })
    throw error
  }
}

// Video Batch Submission - Atomic operation
export async function submitOptimizedVideoBatch(userId: string, videoCount: number = 50) {
  try {
    console.log('🚀 Using optimized video batch submission...')
    const result = await submitVideoBatchFunction({ 
      userId, 
      videoCount 
    })
    
    if (result.data && typeof result.data === 'object' && 'success' in result.data) {
      const data = result.data as { success: boolean; data: any }
      if (data.success) {
        console.log('✅ Video batch submitted via optimized function')
        return data.data
      }
    }
    
    throw new Error('Invalid response from video batch function')
  } catch (error) {
    console.error('❌ Error in optimized video batch submission:', error)
    throw error
  }
}

// Withdrawal Request - Atomic operation with validation
export async function processOptimizedWithdrawal(withdrawalData: {
  amount: number
  bankDetails: {
    accountHolderName: string
    accountNumber: string
    ifscCode: string
    bankName: string
  }
}) {
  try {
    console.log('🚀 Using optimized withdrawal processing...')
    const result = await processWithdrawalRequestFunction(withdrawalData)
    
    if (result.data && typeof result.data === 'object' && 'success' in result.data) {
      const data = result.data as { success: boolean; data: any }
      if (data.success) {
        console.log('✅ Withdrawal processed via optimized function')
        return data.data
      }
    }
    
    throw new Error('Invalid response from withdrawal function')
  } catch (error) {
    console.error('❌ Error in optimized withdrawal processing:', error)
    throw error
  }
}

// User Notifications - Server-side filtering
export async function getOptimizedUserNotifications(userId: string, limit: number = 10) {
  try {
    console.log('🚀 Using optimized notifications function...')
    const result = await getUserNotificationsFunction({ 
      userId, 
      limit 
    })
    
    if (result.data && typeof result.data === 'object' && 'success' in result.data) {
      const data = result.data as { success: boolean; data: any }
      if (data.success) {
        console.log('✅ Notifications loaded via optimized function')
        return data.data
      }
    }
    
    throw new Error('Invalid response from notifications function')
  } catch (error) {
    console.error('❌ Error in optimized notifications:', error)
    throw error
  }
}

// User Transactions - Optimized with pagination
export async function getOptimizedUserTransactions(userId: string, limit: number = 10, type: string = 'all') {
  try {
    console.log('🚀 Using optimized transactions function...')
    const result = await getUserTransactionsFunction({ 
      userId, 
      limit, 
      type 
    })
    
    if (result.data && typeof result.data === 'object' && 'success' in result.data) {
      const data = result.data as { success: boolean; data: any }
      if (data.success) {
        console.log('✅ Transactions loaded via optimized function')
        return data.data
      }
    }
    
    throw new Error('Invalid response from transactions function')
  } catch (error) {
    console.error('❌ Error in optimized transactions:', error)
    throw error
  }
}

// Admin Withdrawals - Batch processing with user data
export async function getOptimizedAdminWithdrawals(showAllWithdrawals: boolean = false) {
  try {
    console.log('🚀 Using optimized admin withdrawals function, showAll:', showAllWithdrawals)
    console.log('🔗 Functions instance:', functions.app.options.projectId)

    const result = await getAdminWithdrawalsFunction({
      showAllWithdrawals
    })
    console.log('📡 Admin withdrawals function response received:', result)

    if (result.data && typeof result.data === 'object' && 'success' in result.data) {
      const data = result.data as { success: boolean; data: any }
      if (data.success) {
        console.log('✅ Admin withdrawals loaded via optimized function')
        return data.data
      } else {
        console.error('❌ Admin withdrawals function returned success: false', data)
        throw new Error('Admin withdrawals function returned success: false')
      }
    }

    console.error('❌ Invalid admin withdrawals function response structure:', result)
    throw new Error('Invalid response from admin withdrawals function')
  } catch (error: any) {
    console.error('❌ Error in optimized admin withdrawals:', error)
    console.error('❌ Error details:', {
      name: error?.name,
      message: error?.message,
      code: error?.code,
      details: error?.details
    })
    throw error
  }
}

// Fallback detection - Check if functions are available
export async function areFunctionsAvailable(): Promise<boolean> {
  try {
    console.log('🔍 Testing Firebase Functions connectivity...')
    console.log('🔗 Functions project:', functions.app.options.projectId)
    console.log('🔗 Functions region:', functions.region)

    // Try a simple function call to test connectivity
    const result = await getUserDashboardDataFunction({ userId: 'test' })
    console.log('✅ Functions are available, test response:', result)
    return true
  } catch (error: any) {
    console.warn('⚠️ Firebase Functions not available, falling back to direct Firestore')
    console.error('❌ Functions test error:', {
      name: error?.name,
      message: error?.message,
      code: error?.code,
      details: error?.details
    })
    return false
  }
}

// Hybrid approach - Use functions if available, fallback to direct calls
export async function getHybridDashboardData(userId: string) {
  const cacheKey = `dashboard-${userId}`
  const now = Date.now()

  // Check cache first for fallback data
  const cached = fallbackCache.get(cacheKey)
  if (cached && (now - cached.timestamp) < cached.ttl) {
    console.log('🎯 Using cached fallback dashboard data')
    return cached.data
  }

  try {
    // Try optimized function first with fast timeout
    const result = await getOptimizedDashboardData(userId)

    // Cache successful function result
    fallbackCache.set(cacheKey, {
      data: result,
      timestamp: now,
      ttl: 60000 // 1 minute cache for function results
    })

    return result
  } catch (error) {
    console.warn('⚠️ Optimized function failed, falling back to direct calls:', error instanceof Error ? error.message : String(error))

    // Fast fallback to original dataService functions
    const { getUserData, getWalletData, getVideoCountData } = await import('./dataService')

    console.log('📊 Loading dashboard data via direct Firestore calls...')
    const startTime = Date.now()

    const [userData, walletData, videoData] = await Promise.all([
      getUserData(userId),
      getWalletData(userId),
      getVideoCountData(userId)
    ])

    const loadTime = Date.now() - startTime
    console.log(`✅ Dashboard data loaded via fallback in ${loadTime}ms`)

    const result = {
      userData,
      walletData,
      videoData
    }

    // Cache fallback result for shorter time
    fallbackCache.set(cacheKey, {
      data: result,
      timestamp: now,
      ttl: 30000 // 30 seconds cache for fallback results
    })

    return result
  }
}

// Admin Dashboard Stats - Server-side aggregation
export async function getOptimizedAdminDashboardStats() {
  try {
    console.log('🚀 Using optimized admin dashboard stats function...')
    const result = await getAdminDashboardStatsFunction({})

    if (result.data && typeof result.data === 'object' && 'success' in result.data) {
      const data = result.data as { success: boolean; data: any }
      if (data.success) {
        console.log('✅ Admin dashboard stats loaded via optimized function')
        return data.data
      }
    }

    throw new Error('Invalid response from admin dashboard stats function')
  } catch (error) {
    console.error('❌ Error in optimized admin dashboard stats:', error)
    throw error
  }
}

// Admin Users - Server-side filtering and pagination
export async function getOptimizedAdminUsers(options: {
  page?: number
  limit?: number
  searchTerm?: string
  planFilter?: string
  sortBy?: string
  sortOrder?: string
} = {}) {
  try {
    console.log('🚀 Using optimized admin users function...')
    const result = await getAdminUsersFunction(options)

    if (result.data && typeof result.data === 'object' && 'success' in result.data) {
      const data = result.data as { success: boolean; data: any }
      if (data.success) {
        console.log('✅ Admin users loaded via optimized function')
        return data.data
      }
    }

    throw new Error('Invalid response from admin users function')
  } catch (error) {
    console.error('❌ Error in optimized admin users:', error)
    throw error
  }
}

// Admin Notifications - Optimized loading
export async function getOptimizedAdminNotifications(limit: number = 50, type: string = 'all') {
  try {
    console.log('🚀 Using optimized admin notifications function...')
    const result = await getAdminNotificationsFunction({ limit, type })

    if (result.data && typeof result.data === 'object' && 'success' in result.data) {
      const data = result.data as { success: boolean; data: any }
      if (data.success) {
        console.log('✅ Admin notifications loaded via optimized function')
        return data.data
      }
    }

    throw new Error('Invalid response from admin notifications function')
  } catch (error) {
    console.error('❌ Error in optimized admin notifications:', error)
    throw error
  }
}

// Create Admin Notification - Optimized creation
export async function createOptimizedAdminNotification(notificationData: {
  title: string
  message: string
  type: string
  targetUsers: string
  userIds?: string[]
}) {
  try {
    console.log('🚀 Using optimized admin notification creation...')
    const result = await createAdminNotificationFunction(notificationData)

    if (result.data && typeof result.data === 'object' && 'success' in result.data) {
      const data = result.data as { success: boolean; data: any }
      if (data.success) {
        console.log('✅ Admin notification created via optimized function')
        return data.data
      }
    }

    throw new Error('Invalid response from admin notification creation function')
  } catch (error) {
    console.error('❌ Error in optimized admin notification creation:', error)
    throw error
  }
}

// Hybrid admin dashboard stats with fallback
export async function getHybridAdminDashboardStats() {
  try {
    // Try optimized function first
    return await getOptimizedAdminDashboardStats()
  } catch (error) {
    console.warn('⚠️ Optimized admin stats function failed, falling back to direct calls')
    // Fallback to original adminDataService
    const { getAdminDashboardStats } = await import('./adminDataService')
    return await getAdminDashboardStats()
  }
}

// Export optimized functions with fallback capability
export const optimizedService = {
  // User functions
  getDashboardData: getHybridDashboardData,
  submitVideoBatch: submitOptimizedVideoBatch,
  processWithdrawal: processOptimizedWithdrawal,
  getUserNotifications: getOptimizedUserNotifications,
  getUserTransactions: getOptimizedUserTransactions,

  // Admin functions
  getAdminWithdrawals: getOptimizedAdminWithdrawals,
  getAdminDashboardStats: getHybridAdminDashboardStats,
  getAdminUsers: getOptimizedAdminUsers,
  getAdminNotifications: getOptimizedAdminNotifications,
  createAdminNotification: createOptimizedAdminNotification,

  // Utility
  areFunctionsAvailable
}
