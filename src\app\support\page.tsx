'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRequireAuth } from '@/hooks/useAuth'
import { useBlockingNotifications } from '@/hooks/useBlockingNotifications'
import BlockingNotificationModal from '@/components/BlockingNotificationModal'
import { getSupportAvailabilityDetails } from '@/lib/supportUtils'

export default function SupportPage() {
  const { user, loading } = useRequireAuth()
  const { hasBlockingNotifications, isChecking, markAllAsRead } = useBlockingNotifications(user?.uid || null)
  const [supportStatus, setSupportStatus] = useState(getSupportAvailabilityDetails())

  // Update support status every minute
  useEffect(() => {
    const interval = setInterval(() => {
      setSupportStatus(getSupportAvailabilityDetails())
    }, 60000) // Update every minute

    return () => clearInterval(interval)
  }, [])

  if (loading || isChecking) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="spinner mb-4"></div>
          <p className="text-white">
            {loading ? 'Loading...' : 'Checking notifications...'}
          </p>
        </div>
      </div>
    )
  }

  // Show blocking notifications if any exist
  if (hasBlockingNotifications && user) {
    return (
      <BlockingNotificationModal
        userId={user.uid}
        onAllRead={markAllAsRead}
      />
    )
  }

  return (
    <div className="min-h-screen p-4">
      {/* Header */}
      <header className="glass-card p-4 mb-6">
        <div className="flex items-center justify-between">
          <Link href="/dashboard" className="glass-button px-4 py-2 text-white">
            <i className="fas fa-arrow-left mr-2"></i>
            Back to Dashboard
          </Link>
          <h1 className="text-xl font-bold text-white">Support & Help</h1>
          <div className="w-24"></div> {/* Spacer for centering */}
        </div>
      </header>

      {/* Support Status */}
      <div className="glass-card p-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold text-white">
            <i className="fas fa-headset mr-2"></i>
            Contact Support
          </h2>
          <div className={`px-3 py-1 rounded-full text-sm font-medium ${
            supportStatus.status === 'online'
              ? 'bg-green-500/20 text-green-400 border border-green-500/30'
              : 'bg-red-500/20 text-red-400 border border-red-500/30'
          }`}>
            <i className={`fas fa-circle mr-2 ${
              supportStatus.status === 'online' ? 'text-green-400' : 'text-red-400'
            }`}></i>
            {supportStatus.status === 'online' ? 'Online' : 'Offline'}
          </div>
        </div>

        <div className={`p-4 rounded-lg mb-6 ${
          supportStatus.status === 'online'
            ? 'bg-green-500/10 border border-green-500/20'
            : 'bg-orange-500/10 border border-orange-500/20'
        }`}>
          <p className={`font-medium mb-2 ${
            supportStatus.status === 'online' ? 'text-green-400' : 'text-orange-400'
          }`}>
            <i className={`fas ${supportStatus.status === 'online' ? 'fa-check-circle' : 'fa-clock'} mr-2`}></i>
            {supportStatus.message}
          </p>
          {supportStatus.nextAvailable && (
            <p className="text-white/60 text-sm">
              {supportStatus.nextAvailable}
            </p>
          )}
          <p className="text-white/60 text-sm mt-2">
            <i className="fas fa-calendar mr-2"></i>
            {supportStatus.hoursInfo}
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 gap-6">
          {/* WhatsApp Support */}
          <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-6">
            <div className="flex items-center mb-4">
              <i className="fab fa-whatsapp text-green-400 text-3xl mr-3"></i>
              <div>
                <h3 className="text-white font-bold text-lg">WhatsApp Support</h3>
                <p className="text-green-300 text-sm">Instant messaging support</p>
              </div>
            </div>
            <p className="text-white/80 mb-4">
              Get instant help via WhatsApp during business hours. Our team responds quickly to your queries on working days.
            </p>
            <div className={`mb-3 p-2 rounded text-sm ${
              supportStatus.status === 'online'
                ? 'bg-green-500/20 text-green-400'
                : 'bg-orange-500/20 text-orange-400'
            }`}>
              <i className={`fas ${supportStatus.status === 'online' ? 'fa-check' : 'fa-clock'} mr-2`}></i>
              {supportStatus.status === 'online'
                ? 'Available now - Usually responds within minutes'
                : 'Currently offline - Will respond when available'
              }
            </div>
            <a
              href="https://wa.me/917676636990"
              target="_blank"
              rel="noopener noreferrer"
              className="btn-success bg-green-500 hover:bg-green-600 w-full"
            >
              <i className="fab fa-whatsapp mr-2"></i>
              Chat on WhatsApp: +91 7676636990
            </a>
          </div>

          {/* Email Support */}
          <div className="bg-blue-500/20 border border-blue-500/30 rounded-lg p-6">
            <div className="flex items-center mb-4">
              <i className="fas fa-envelope text-blue-400 text-3xl mr-3"></i>
              <div>
                <h3 className="text-white font-bold text-lg">Email Support</h3>
                <p className="text-blue-300 text-sm">Detailed support via email</p>
              </div>
            </div>
            <p className="text-white/80 mb-4">
              Send us detailed queries and we'll respond within 24 hours on working days.
            </p>
            <div className={`mb-3 p-2 rounded text-sm ${
              supportStatus.status === 'online'
                ? 'bg-blue-500/20 text-blue-400'
                : 'bg-orange-500/20 text-orange-400'
            }`}>
              <i className={`fas ${supportStatus.status === 'online' ? 'fa-check' : 'fa-clock'} mr-2`}></i>
              {supportStatus.status === 'online'
                ? 'Available now - Response within 24 hours'
                : 'Currently offline - Will respond on next working day'
              }
            </div>
            <a
              href="mailto:<EMAIL>"
              className="btn-primary bg-blue-500 hover:bg-blue-600 w-full"
            >
              <i className="fas fa-envelope mr-2"></i>
              Email: <EMAIL>
            </a>
          </div>
        </div>
      </div>

      {/* FAQ Section */}
      <div className="glass-card p-6 mb-6">
        <h2 className="text-2xl font-bold text-white mb-6">
          <i className="fas fa-question-circle mr-2"></i>
          Frequently Asked Questions
        </h2>
        
        <div className="space-y-4">
          {/* FAQ Item 1 */}
          <div className="bg-white/10 rounded-lg p-4">
            <h3 className="text-white font-semibold mb-2">
              <i className="fas fa-play-circle mr-2 text-youtube-red"></i>
              How do I earn money by watching videos?
            </h3>
            <p className="text-white/80 text-sm">
              Watch videos completely to earn money. You earn ₹10-400 per batch of 1000 videos depending on your plan.
              Videos must be watched for the full duration to count towards your earnings.
            </p>
          </div>

          {/* FAQ Item 2 */}
          <div className="bg-white/10 rounded-lg p-4">
            <h3 className="text-white font-semibold mb-2">
              <i className="fas fa-wallet mr-2 text-green-400"></i>
              When can I withdraw my earnings?
            </h3>
            <p className="text-white/80 text-sm">
              Withdrawals are available between 10:00 AM to 6:00 PM on non-leave days.
              Minimum withdrawal is ₹50. Trial users cannot withdraw - upgrade to a paid plan first.
            </p>
          </div>

          {/* FAQ Item 3 */}
          <div className="bg-white/10 rounded-lg p-4">
            <h3 className="text-white font-semibold mb-2">
              <i className="fas fa-users mr-2 text-blue-400"></i>
              How does the referral system work?
            </h3>
            <p className="text-white/80 text-sm">
              Share your referral code with friends. When they join and purchase a plan, 
              you earn a bonus ranging from ₹50 to ₹1200 depending on the plan they choose.
            </p>
          </div>

          {/* FAQ Item 4 */}
          <div className="bg-white/10 rounded-lg p-4">
            <h3 className="text-white font-semibold mb-2">
              <i className="fas fa-crown mr-2 text-yellow-400"></i>
              What are the different plans available?
            </h3>
            <p className="text-white/80 text-sm">
              We offer Trial (free), Starter (₹499), Basic (₹1499), Premium (₹2999), Gold (₹3999), 
              Platinum (₹5999), and Diamond (₹9999) plans with different earning rates and video durations.
            </p>
          </div>

          {/* FAQ Item 5 */}
          <div className="bg-white/10 rounded-lg p-4">
            <h3 className="text-white font-semibold mb-2">
              <i className="fas fa-bolt mr-2 text-orange-400"></i>
              What is Quick Video Advantage?
            </h3>
            <p className="text-white/80 text-sm">
              Admins can grant temporary quick video advantage that reduces your video duration to 30 seconds 
              for a limited period, helping you complete tasks faster.
            </p>
          </div>
        </div>
      </div>

      {/* Support Hours */}
      <div className="glass-card p-6">
        <h2 className="text-xl font-bold text-white mb-4">
          <i className="fas fa-clock mr-2"></i>
          Support Hours
        </h2>
        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-white font-semibold mb-2">WhatsApp Support</h3>
            <p className="text-white/80 text-sm mb-1">Monday - Friday: 9:00 AM - 6:00 PM</p>
            <p className="text-white/60 text-sm mb-1">Working days only</p>
            <p className="text-green-400 text-sm">Usually responds within minutes during business hours</p>
          </div>
          <div>
            <h3 className="text-white font-semibold mb-2">Email Support</h3>
            <p className="text-white/80 text-sm mb-1">Monday - Friday: 9:00 AM - 6:00 PM</p>
            <p className="text-white/60 text-sm mb-1">Working days only</p>
            <p className="text-blue-400 text-sm">Response within 24 hours on working days</p>
          </div>
        </div>
      </div>
    </div>
  )
}
