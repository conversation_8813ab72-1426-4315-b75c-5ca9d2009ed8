(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6779,8925,9160],{2728:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>y});var s=a(5155),n=a(2115),o=a(6681),r=a(6779),i=a(3592),l=a(6273),c=a(6104),d=a(5317);async function u(){let e={analyzed:0,needsRestoration:[],restored:0,errors:[]};try{console.log("\uD83D\uDD0D Starting active days restoration analysis...");let t=await (0,d.getDocs)((0,d.collection)(c.db,i.COLLECTIONS.users)),a=new Date;for(let s of(a.setHours(0,0,0,0),t.docs))try{let t=s.data(),n=s.id;if(e.analyzed++,!t[i.FIELD_NAMES.joinedDate])continue;let o=t[i.FIELD_NAMES.joinedDate].toDate();o.setHours(0,0,0,0);let r=t[i.FIELD_NAMES.activeDays]||1,l=t[i.FIELD_NAMES.plan]||"Trial",c=t[i.FIELD_NAMES.manuallySetActiveDays]||!1,d=Math.floor((a.getTime()-o.getTime())/864e5),u=1,m=!1,h="";if(c)h="Manually set by admin - skipping";else if("Trial"===l){let e=Math.min(d+1,10);r>e&&(u=Math.max(1,Math.min(d+1,3)),m=!0,h="Trial user with ".concat(r," active days (joined ").concat(d," days ago)"))}else{let e=d+5;r>e&&(u=Math.max(1,Math.min(d+1,30)),m=!0,h="Paid user with ".concat(r," active days (joined ").concat(d," days ago)"))}m&&e.needsRestoration.push({userId:n,email:t[i.FIELD_NAMES.email]||"Unknown",plan:l,currentActiveDays:r,joinedDate:o,daysSinceJoined:d,suggestedActiveDays:u,shouldRestore:m,reason:h})}catch(t){e.errors.push("Error processing user ".concat(s.id,": ").concat(t))}return console.log("✅ Analysis complete. Found ".concat(e.needsRestoration.length," users needing restoration out of ").concat(e.analyzed," total users.")),e}catch(t){return console.error("Error in active days restoration analysis:",t),e.errors.push("Analysis error: ".concat(t)),e}}async function m(e){let t={restored:0,errors:[]};try{console.log("\uD83D\uDD27 Starting restoration for ".concat(e.length," users..."));let a=(0,d.wP)(c.db),s=0;for(let n of e)try{let e=(0,d.H9)(c.db,i.COLLECTIONS.users,n.userId);a.update(e,{[i.FIELD_NAMES.activeDays]:n.suggestedActiveDays,[i.FIELD_NAMES.manuallySetActiveDays]:!1,[i.FIELD_NAMES.lastActiveDaysUpdate]:new Date}),s++,t.restored++,s>=500&&(await a.commit(),a=(0,d.wP)(c.db),s=0,console.log("\uD83D\uDCE6 Committed batch of ".concat(500," updates")))}catch(e){t.errors.push("Error restoring user ".concat(n.email,": ").concat(e))}return s>0&&(await a.commit(),console.log("\uD83D\uDCE6 Committed final batch of ".concat(s," updates"))),console.log("✅ Restoration complete. Restored ".concat(t.restored," users.")),t}catch(e){return console.error("Error in active days restoration:",e),t.errors.push("Restoration error: ".concat(e)),t}}async function h(){try{console.log("\uD83D\uDCBE Creating active days backup...");let e=await (0,d.getDocs)((0,d.collection)(c.db,i.COLLECTIONS.users)),t=[];for(let a of e.docs){let e=a.data();t.push({userId:a.id,email:e[i.FIELD_NAMES.email],activeDays:e[i.FIELD_NAMES.activeDays],manuallySetActiveDays:e[i.FIELD_NAMES.manuallySetActiveDays],lastActiveDaysUpdate:e[i.FIELD_NAMES.lastActiveDaysUpdate],plan:e[i.FIELD_NAMES.plan],joinedDate:e[i.FIELD_NAMES.joinedDate],backupTimestamp:new Date})}return console.log("✅ Backup created for ".concat(t.length," users")),{success:!0,backupData:t}}catch(e){return console.error("Error creating backup:",e),{success:!1,backupData:[],error:String(e)}}}var g=a(4752),p=a.n(g);function y(){let{isAdmin:e,loading:t}=(0,o.wC)(),[a,c]=(0,n.useState)([]),[d,g]=(0,n.useState)(!1),[y,f]=(0,n.useState)(!1),[x,w]=(0,n.useState)(!1),[v,D]=(0,n.useState)(null),[b,E]=(0,n.useState)(!1),[N,A]=(0,n.useState)(null),j=async()=>{try{g(!0),c([]),console.log("\uD83D\uDD0D Starting active days debug check...");let e=(await (0,r.CF)()).slice(0,10),t=[];for(let a of e)try{let e=a.activeDays||0,s=await (0,i.calculateUserActiveDays)(a.id),n=e!==s;t.push({userId:a.id,email:a.email,plan:a.plan,joinedDate:a.joinedDate,planExpiry:a.planExpiry,storedActiveDays:e,calculatedActiveDays:s,discrepancy:n,manuallySet:a.manuallySetActiveDays||!1,lastUpdate:a.lastActiveDaysUpdate}),console.log("User ".concat(a.email,": Stored=").concat(e,", Calculated=").concat(s,", Discrepancy=").concat(n))}catch(e){console.error("Error processing user ".concat(a.id,":"),e),t.push({userId:a.id,email:a.email,error:e.message})}c(t),console.log("✅ Debug check completed")}catch(e){console.error("Error in debug check:",e)}finally{g(!1)}},S=async()=>{try{f(!0),console.log("\uD83E\uDDEA Testing Firebase function after deployment...");let e=await l.x8.getDashboardData("current-user");if(e.userData)p().fire({icon:"success",title:"Firebase Function Test Successful!",html:'\n            <div class="text-left">\n              <p><strong>User Active Days:</strong> '.concat(e.userData.activeDays,"</p>\n              <p><strong>Plan:</strong> ").concat(e.userData.plan,"</p>\n              <p><strong>Status:</strong> Firebase function is now using stored database values</p>\n            </div>\n          "),timer:5e3,showConfirmButton:!0});else throw Error("Function test failed")}catch(e){console.error("Error testing Firebase function:",e),p().fire({icon:"error",title:"Firebase Function Test Failed",text:"There was an error testing the deployed function."})}finally{f(!1)}},L=async()=>{try{w(!0),D(null),console.log("\uD83D\uDD0D Analyzing active days issue across all users...");let e=await (0,r.CF)(),t={totalUsers:e.length,trialUsers:0,paidUsers:0,suspiciouslyHighActiveDays:[],normalActiveDays:[],manuallySetUsers:[],recentlyJoinedButHighActiveDays:[]},a=new Date;for(let s of e){let e=s.activeDays||0,n=s.plan||"Unknown",o=s.joinedDate?new Date(1e3*s.joinedDate.seconds):null,r=s.manuallySetActiveDays||!1;if("Trial"===n?t.trialUsers++:t.paidUsers++,r&&t.manuallySetUsers.push({email:s.email,plan:n,activeDays:e,joinedDate:o}),o){let r=Math.floor((a.getTime()-o.getTime())/864e5);e>r+5?t.suspiciouslyHighActiveDays.push({email:s.email,plan:n,activeDays:e,daysSinceJoined:r,joinedDate:o,difference:e-r}):t.normalActiveDays.push({email:s.email,plan:n,activeDays:e,daysSinceJoined:r}),r<=10&&e>15&&t.recentlyJoinedButHighActiveDays.push({email:s.email,plan:n,activeDays:e,daysSinceJoined:r,joinedDate:o})}}D(t),console.log("✅ Analysis completed:",t),p().fire({icon:"info",title:"Active Days Analysis Complete",html:'\n          <div class="text-left">\n            <p><strong>Total Users:</strong> '.concat(t.totalUsers,"</p>\n            <p><strong>Trial Users:</strong> ").concat(t.trialUsers,"</p>\n            <p><strong>Paid Users:</strong> ").concat(t.paidUsers,"</p>\n            <p><strong>Suspicious High Active Days:</strong> ").concat(t.suspiciouslyHighActiveDays.length,"</p>\n            <p><strong>Normal Active Days:</strong> ").concat(t.normalActiveDays.length,"</p>\n            <p><strong>Manually Set by Admin:</strong> ").concat(t.manuallySetUsers.length,"</p>\n          </div>\n        "),showConfirmButton:!0})}catch(e){console.error("Error analyzing active days:",e),p().fire({icon:"error",title:"Analysis Failed",text:"There was an error analyzing the active days data."})}finally{w(!1)}},C=async()=>{try{if(!(await p().fire({icon:"warning",title:"Restore Active Days?",html:'\n          <div class="text-left">\n            <p><strong>⚠️ This will:</strong></p>\n            <ul class="list-disc list-inside mt-2 space-y-1">\n              <li>Analyze all users for incorrect active days</li>\n              <li>Create a backup of current values</li>\n              <li>Restore reasonable active days based on join dates</li>\n              <li>Reset manual override flags</li>\n            </ul>\n            <p class="mt-4 text-red-600"><strong>This action cannot be easily undone!</strong></p>\n          </div>\n        ',showCancelButton:!0,confirmButtonText:"Yes, Restore Active Days",cancelButtonText:"Cancel",confirmButtonColor:"#dc2626"})).isConfirmed)return;E(!0),console.log("\uD83D\uDD27 Starting active days restoration process..."),p().fire({title:"Creating Backup...",text:"Please wait while we backup current active days data.",allowOutsideClick:!1,didOpen:()=>{p().showLoading()}});let e=await h();if(!e.success)throw Error("Backup failed: ".concat(e.error));p().update({title:"Analyzing Users...",text:"Analyzing all users to identify incorrect active days."});let t=await u();if(0===t.needsRestoration.length)return void p().fire({icon:"success",title:"No Issues Found!",text:"All users have reasonable active days values. No restoration needed."});if(!(await p().fire({icon:"question",title:"Found ".concat(t.needsRestoration.length," Users to Restore"),html:'\n          <div class="text-left">\n            <p><strong>Users needing restoration:</strong> '.concat(t.needsRestoration.length,"</p>\n            <p><strong>Total users analyzed:</strong> ").concat(t.analyzed,'</p>\n            <div class="mt-4 max-h-40 overflow-y-auto bg-gray-50 p-3 rounded">\n              ').concat(t.needsRestoration.slice(0,10).map(e=>'<div class="text-sm mb-1">\n                  <strong>'.concat(e.email,"</strong> (").concat(e.plan,"): ").concat(e.currentActiveDays," → ").concat(e.suggestedActiveDays," days\n                </div>")).join(""),"\n              ").concat(t.needsRestoration.length>10?'<div class="text-sm text-gray-600">... and '.concat(t.needsRestoration.length-10," more</div>"):"","\n            </div>\n          </div>\n        "),showCancelButton:!0,confirmButtonText:"Proceed with Restoration",cancelButtonText:"Cancel"})).isConfirmed)return;p().fire({title:"Restoring Active Days...",text:"Updating ".concat(t.needsRestoration.length," users..."),allowOutsideClick:!1,didOpen:()=>{p().showLoading()}});let a=await m(t.needsRestoration);A({backup:e,analysis:t,restoration:a}),p().fire({icon:"success",title:"Active Days Restoration Complete!",html:'\n          <div class="text-left">\n            <p><strong>✅ Backup Created:</strong> '.concat(e.backupData.length," users</p>\n            <p><strong>\uD83D\uDD0D Users Analyzed:</strong> ").concat(t.analyzed,"</p>\n            <p><strong>\uD83D\uDD27 Users Restored:</strong> ").concat(a.restored,"</p>\n            <p><strong>❌ Errors:</strong> ").concat(a.errors.length,"</p>\n            ").concat(a.errors.length>0?'\n              <div class="mt-2 text-red-600 text-sm">\n                <strong>Errors:</strong><br>\n                '.concat(a.errors.slice(0,3).join("<br>"),"\n                ").concat(a.errors.length>3?"<br>... and ".concat(a.errors.length-3," more"):"","\n              </div>\n            "):"","\n          </div>\n        "),timer:1e4,showConfirmButton:!0}),console.log("✅ Active days restoration completed successfully")}catch(e){console.error("Error in active days restoration:",e),p().fire({icon:"error",title:"Restoration Failed",text:"There was an error during restoration: ".concat(e)})}finally{E(!1)}};return t?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}):e?(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,s.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Debug Active Days"}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:"Compare stored Firestore values vs calculated values for active days"})]}),(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex space-x-4 mb-6",children:[(0,s.jsx)("button",{onClick:j,disabled:d,className:"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50",children:d?"Processing...":"Run Debug Check (First 10 Users)"}),(0,s.jsx)("button",{onClick:S,disabled:y,className:"bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 disabled:opacity-50",children:y?"Testing...":"Test Fixed Firebase Function"}),(0,s.jsx)("button",{onClick:L,disabled:x,className:"bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700 disabled:opacity-50",children:x?"Analyzing...":"Analyze Active Days Issue"}),(0,s.jsx)("button",{onClick:C,disabled:b,className:"bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 disabled:opacity-50",children:b?"Restoring...":"\uD83D\uDD27 Restore Active Days"})]}),(0,s.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6",children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("i",{className:"fas fa-info-circle text-yellow-600 mt-1"})}),(0,s.jsxs)("div",{className:"ml-3",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-yellow-800",children:"Active Days Fix Status"}),(0,s.jsxs)("div",{className:"mt-2 text-sm text-yellow-700",children:[(0,s.jsxs)("p",{children:["✅ ",(0,s.jsx)("strong",{children:"Firebase Function Fixed:"})," Now uses stored database values instead of recalculating"]}),(0,s.jsxs)("p",{children:["\uD83D\uDCCA ",(0,s.jsx)("strong",{children:"Database Values:"})," The stored activeDays in Firestore are correct"]}),(0,s.jsxs)("p",{children:["\uD83D\uDD27 ",(0,s.jsx)("strong",{children:"What Changed:"})," Withdrawals page and admin functions now show accurate active days"]})]})]})]})}),v&&(0,s.jsxs)("div",{className:"mt-8",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Active Days Issue Analysis"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6",children:[(0,s.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,s.jsx)("h3",{className:"font-medium text-blue-900",children:"Total Users"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:v.totalUsers})]}),(0,s.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg",children:[(0,s.jsx)("h3",{className:"font-medium text-green-900",children:"Normal Active Days"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-green-600",children:v.normalActiveDays.length})]}),(0,s.jsxs)("div",{className:"bg-red-50 p-4 rounded-lg",children:[(0,s.jsx)("h3",{className:"font-medium text-red-900",children:"Suspicious High"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-red-600",children:v.suspiciouslyHighActiveDays.length})]}),(0,s.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[(0,s.jsx)("h3",{className:"font-medium text-yellow-900",children:"Manually Set"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:v.manuallySetUsers.length})]})]}),v.suspiciouslyHighActiveDays.length>0&&(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6",children:[(0,s.jsx)("h3",{className:"font-medium text-red-900 mb-3",children:"\uD83D\uDEA8 Users with Suspiciously High Active Days"}),(0,s.jsxs)("div",{className:"max-h-60 overflow-y-auto",children:[(0,s.jsxs)("table",{className:"min-w-full",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"text-left text-xs font-medium text-red-700 uppercase",children:"Email"}),(0,s.jsx)("th",{className:"text-left text-xs font-medium text-red-700 uppercase",children:"Plan"}),(0,s.jsx)("th",{className:"text-left text-xs font-medium text-red-700 uppercase",children:"Active Days"}),(0,s.jsx)("th",{className:"text-left text-xs font-medium text-red-700 uppercase",children:"Days Since Joined"}),(0,s.jsx)("th",{className:"text-left text-xs font-medium text-red-700 uppercase",children:"Difference"})]})}),(0,s.jsx)("tbody",{children:v.suspiciouslyHighActiveDays.slice(0,10).map((e,t)=>(0,s.jsxs)("tr",{className:"text-sm",children:[(0,s.jsx)("td",{className:"py-1 text-red-900",children:e.email}),(0,s.jsx)("td",{className:"py-1 text-red-900",children:e.plan}),(0,s.jsx)("td",{className:"py-1 font-bold text-red-600",children:e.activeDays}),(0,s.jsx)("td",{className:"py-1 text-red-900",children:e.daysSinceJoined}),(0,s.jsxs)("td",{className:"py-1 font-bold text-red-600",children:["+",e.difference]})]},t))})]}),v.suspiciouslyHighActiveDays.length>10&&(0,s.jsxs)("p",{className:"text-sm text-red-600 mt-2",children:["... and ",v.suspiciouslyHighActiveDays.length-10," more users"]})]})]})]}),a.length>0&&(0,s.jsxs)("div",{className:"mt-8",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Debug Results"}),(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Plan"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Stored Active Days"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Calculated Active Days"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Discrepancy"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Manually Set"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:a.map((e,t)=>(0,s.jsxs)("tr",{className:e.discrepancy?"bg-red-50":"",children:[(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.email}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:e.userId})]}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.plan}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.storedActiveDays}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.calculatedActiveDays}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:e.discrepancy?(0,s.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800",children:"YES"}):(0,s.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800",children:"NO"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.manuallySet?"Yes":"No"})]},t))})]})})]})]})]})})}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:"Access Denied"}),(0,s.jsx)("p",{className:"text-gray-600",children:"You don't have permission to access this page."})]})})}},6273:(e,t,a)=>{"use strict";a.d(t,{x8:()=>x});var s=a(2144),n=a(6104);let o=(0,s.Qg)(n.Cn,"getUserDashboardData"),r=(0,s.Qg)(n.Cn,"submitVideoBatch"),i=(0,s.Qg)(n.Cn,"processWithdrawalRequest"),l=(0,s.Qg)(n.Cn,"getUserNotifications"),c=(0,s.Qg)(n.Cn,"getUserTransactions"),d=(0,s.Qg)(n.Cn,"getAdminWithdrawals"),u=(0,s.Qg)(n.Cn,"getAdminDashboardStats"),m=(0,s.Qg)(n.Cn,"getAdminUsers"),h=(0,s.Qg)(n.Cn,"getAdminNotifications"),g=(0,s.Qg)(n.Cn,"createAdminNotification"),p=new Map;async function y(e){try{console.log("\uD83D\uDE80 Using optimized dashboard data function for user:",e),console.log("\uD83D\uDD17 Functions instance:",n.Cn.app.options.projectId);let t=new Promise((e,t)=>{setTimeout(()=>t(Error("Function timeout")),5e3)}),a=await Promise.race([o({userId:e}),t]);if(console.log("\uD83D\uDCE1 Function response received:",a),a&&"object"==typeof a&&a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success){console.log("✅ Dashboard data loaded via optimized function");let t=e.data;return{userData:{name:t.user.name,email:t.user.email,mobile:t.user.mobile,referralCode:t.user.referralCode,plan:t.user.plan,planExpiry:null,activeDays:t.user.activeDays},walletData:{wallet:t.user.wallet},videoData:{totalVideos:t.videos.total,todayVideos:t.videos.today,remainingVideos:t.videos.remaining}}}throw console.error("❌ Function returned success: false",e),Error("Function returned success: false")}throw console.error("❌ Invalid function response structure:",a),Error("Invalid response from dashboard function")}catch(e){throw console.error("❌ Error in optimized dashboard data:",e),console.error("❌ Error details:",{name:null==e?void 0:e.name,message:null==e?void 0:e.message,code:null==e?void 0:e.code,details:null==e?void 0:e.details}),e}}async function f(){try{console.log("\uD83D\uDE80 Using optimized admin dashboard stats function...");let e=await u({});if(e.data&&"object"==typeof e.data&&"success"in e.data){let t=e.data;if(t.success)return console.log("✅ Admin dashboard stats loaded via optimized function"),t.data}throw Error("Invalid response from admin dashboard stats function")}catch(e){throw console.error("❌ Error in optimized admin dashboard stats:",e),e}}let x={getDashboardData:async function(e){let t="dashboard-".concat(e),s=Date.now(),n=p.get(t);if(n&&s-n.timestamp<n.ttl)return console.log("\uD83C\uDFAF Using cached fallback dashboard data"),n.data;try{let a=await y(e);return p.set(t,{data:a,timestamp:s,ttl:6e4}),a}catch(h){console.warn("⚠️ Optimized function failed, falling back to direct calls:",h instanceof Error?h.message:String(h));let{getUserData:n,getWalletData:o,getVideoCountData:r}=await a.e(3592).then(a.bind(a,3592));console.log("\uD83D\uDCCA Loading dashboard data via direct Firestore calls...");let i=Date.now(),[l,c,d]=await Promise.all([n(e),o(e),r(e)]),u=Date.now()-i;console.log("✅ Dashboard data loaded via fallback in ".concat(u,"ms"));let m={userData:l,walletData:c,videoData:d};return p.set(t,{data:m,timestamp:s,ttl:3e4}),m}},submitVideoBatch:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;try{console.log("\uD83D\uDE80 Using optimized video batch submission...");let a=await r({userId:e,videoCount:t});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Video batch submitted via optimized function"),e.data}throw Error("Invalid response from video batch function")}catch(e){throw console.error("❌ Error in optimized video batch submission:",e),e}},processWithdrawal:async function(e){try{console.log("\uD83D\uDE80 Using optimized withdrawal processing...");let t=await i(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Withdrawal processed via optimized function"),e.data}throw Error("Invalid response from withdrawal function")}catch(e){throw console.error("❌ Error in optimized withdrawal processing:",e),e}},getUserNotifications:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;try{console.log("\uD83D\uDE80 Using optimized notifications function...");let a=await l({userId:e,limit:t});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Notifications loaded via optimized function"),e.data}throw Error("Invalid response from notifications function")}catch(e){throw console.error("❌ Error in optimized notifications:",e),e}},getUserTransactions:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"all";try{console.log("\uD83D\uDE80 Using optimized transactions function...");let s=await c({userId:e,limit:t,type:a});if(s.data&&"object"==typeof s.data&&"success"in s.data){let e=s.data;if(e.success)return console.log("✅ Transactions loaded via optimized function"),e.data}throw Error("Invalid response from transactions function")}catch(e){throw console.error("❌ Error in optimized transactions:",e),e}},getAdminWithdrawals:async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{console.log("\uD83D\uDE80 Using optimized admin withdrawals function, showAll:",e),console.log("\uD83D\uDD17 Functions instance:",n.Cn.app.options.projectId);let t=await d({showAllWithdrawals:e});if(console.log("\uD83D\uDCE1 Admin withdrawals function response received:",t),t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin withdrawals loaded via optimized function"),e.data;throw console.error("❌ Admin withdrawals function returned success: false",e),Error("Admin withdrawals function returned success: false")}throw console.error("❌ Invalid admin withdrawals function response structure:",t),Error("Invalid response from admin withdrawals function")}catch(e){throw console.error("❌ Error in optimized admin withdrawals:",e),console.error("❌ Error details:",{name:null==e?void 0:e.name,message:null==e?void 0:e.message,code:null==e?void 0:e.code,details:null==e?void 0:e.details}),e}},getAdminDashboardStats:async function(){try{return await f()}catch(t){console.warn("⚠️ Optimized admin stats function failed, falling back to direct calls");let{getAdminDashboardStats:e}=await Promise.all([a.e(3592),a.e(6779)]).then(a.bind(a,6779));return await e()}},getAdminUsers:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{console.log("\uD83D\uDE80 Using optimized admin users function...");let t=await m(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin users loaded via optimized function"),e.data}throw Error("Invalid response from admin users function")}catch(e){throw console.error("❌ Error in optimized admin users:",e),e}},getAdminNotifications:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"all";try{console.log("\uD83D\uDE80 Using optimized admin notifications function...");let a=await h({limit:e,type:t});if(a.data&&"object"==typeof a.data&&"success"in a.data){let e=a.data;if(e.success)return console.log("✅ Admin notifications loaded via optimized function"),e.data}throw Error("Invalid response from admin notifications function")}catch(e){throw console.error("❌ Error in optimized admin notifications:",e),e}},createAdminNotification:async function(e){try{console.log("\uD83D\uDE80 Using optimized admin notification creation...");let t=await g(e);if(t.data&&"object"==typeof t.data&&"success"in t.data){let e=t.data;if(e.success)return console.log("✅ Admin notification created via optimized function"),e.data}throw Error("Invalid response from admin notification creation function")}catch(e){throw console.error("❌ Error in optimized admin notification creation:",e),e}},areFunctionsAvailable:async function(){try{console.log("\uD83D\uDD0D Testing Firebase Functions connectivity..."),console.log("\uD83D\uDD17 Functions project:",n.Cn.app.options.projectId),console.log("\uD83D\uDD17 Functions region:",n.Cn.region);let e=await o({userId:"test"});return console.log("✅ Functions are available, test response:",e),!0}catch(e){return console.warn("⚠️ Firebase Functions not available, falling back to direct Firestore"),console.error("❌ Functions test error:",{name:null==e?void 0:e.name,message:null==e?void 0:e.message,code:null==e?void 0:e.code,details:null==e?void 0:e.details}),!1}}}},6463:(e,t,a)=>{Promise.resolve().then(a.bind(a,2728))},6779:(e,t,a)=>{"use strict";a.d(t,{CF:()=>d,I0:()=>m,TK:()=>y,getAdminDashboardStats:()=>i,getAllPendingWithdrawals:()=>h,getAllWithdrawals:()=>g,hG:()=>f,lo:()=>l,nQ:()=>u,r2:()=>p,updateWithdrawalStatus:()=>x,x5:()=>c});var s=a(5317),n=a(6104),o=a(3592);let r=new Map;async function i(){let e="dashboard-stats",t=function(e){let t=r.get(e);return t&&Date.now()-t.timestamp<3e5?t.data:null}(e);if(t)return t;try{let t=new Date;t.setHours(0,0,0,0);let a=s.Dc.fromDate(t),i=await (0,s.getDocs)((0,s.collection)(n.db,o.COLLECTIONS.users)),l=i.size,c=(0,s.P)((0,s.collection)(n.db,o.COLLECTIONS.users),(0,s._M)(o.FIELD_NAMES.joinedDate,">=",a)),d=(await (0,s.getDocs)(c)).size,u=0,m=0,h=0,g=0;i.forEach(e=>{var a;let s=e.data();u+=s[o.FIELD_NAMES.totalVideos]||0,m+=s[o.FIELD_NAMES.wallet]||0;let n=null==(a=s[o.FIELD_NAMES.lastVideoDate])?void 0:a.toDate();n&&n.toDateString()===t.toDateString()&&(h+=s[o.FIELD_NAMES.todayVideos]||0)});try{let e=(0,s.P)((0,s.collection)(n.db,o.COLLECTIONS.transactions),(0,s._M)(o.FIELD_NAMES.type,"==","video_earning"),(0,s.AB)(1e3));(await (0,s.getDocs)(e)).forEach(e=>{var a;let s=e.data(),n=null==(a=s[o.FIELD_NAMES.date])?void 0:a.toDate();n&&n>=t&&(g+=s[o.FIELD_NAMES.amount]||0)})}catch(e){console.warn("Could not fetch today's transactions:",e)}let p=(0,s.P)((0,s.collection)(n.db,o.COLLECTIONS.withdrawals),(0,s._M)("status","==","pending")),y=(await (0,s.getDocs)(p)).size,f=(0,s.P)((0,s.collection)(n.db,o.COLLECTIONS.withdrawals),(0,s._M)("date",">=",a)),x=(await (0,s.getDocs)(f)).size,w={totalUsers:l,totalVideos:u,totalEarnings:m,pendingWithdrawals:y,todayUsers:d,todayVideos:h,todayEarnings:g,todayWithdrawals:x};return r.set(e,{data:w,timestamp:Date.now()}),w}catch(e){throw console.error("Error getting admin dashboard stats:",e),e}}async function l(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,s.P)((0,s.collection)(n.db,o.COLLECTIONS.users),(0,s.My)(o.FIELD_NAMES.joinedDate,"desc"),(0,s.AB)(e));t&&(a=(0,s.P)((0,s.collection)(n.db,o.COLLECTIONS.users),(0,s.My)(o.FIELD_NAMES.joinedDate,"desc"),(0,s.HM)(t),(0,s.AB)(e)));let r=await (0,s.getDocs)(a);return{users:r.docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[o.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[o.FIELD_NAMES.planExpiry])?void 0:a.toDate()}}),lastDoc:r.docs[r.docs.length-1]||null,hasMore:r.docs.length===e}}catch(e){throw console.error("Error getting users:",e),e}}async function c(e){try{if(!e||0===e.trim().length)return[];let t=e.toLowerCase().trim(),a=(0,s.P)((0,s.collection)(n.db,o.COLLECTIONS.users),(0,s.My)(o.FIELD_NAMES.joinedDate,"desc"));return(await (0,s.getDocs)(a)).docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[o.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[o.FIELD_NAMES.planExpiry])?void 0:a.toDate()}}).filter(e=>{let a=String(e[o.FIELD_NAMES.name]||"").toLowerCase(),s=String(e[o.FIELD_NAMES.email]||"").toLowerCase(),n=String(e[o.FIELD_NAMES.mobile]||"").toLowerCase(),r=String(e[o.FIELD_NAMES.referralCode]||"").toLowerCase();return a.includes(t)||s.includes(t)||n.includes(t)||r.includes(t)})}catch(e){throw console.error("Error searching users:",e),e}}async function d(){try{let e=(0,s.P)((0,s.collection)(n.db,o.COLLECTIONS.users),(0,s.My)(o.FIELD_NAMES.joinedDate,"desc"));return(await (0,s.getDocs)(e)).docs.map(e=>{var t,a;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[o.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(a=e.data()[o.FIELD_NAMES.planExpiry])?void 0:a.toDate()}})}catch(e){throw console.error("Error getting all users:",e),e}}async function u(){try{let e=(0,s.P)((0,s.collection)(n.db,o.COLLECTIONS.users));return(await (0,s.getDocs)(e)).size}catch(e){throw console.error("Error getting total user count:",e),e}}async function m(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let a=(0,s.P)((0,s.collection)(n.db,o.COLLECTIONS.transactions),(0,s.My)(o.FIELD_NAMES.date,"desc"),(0,s.AB)(e));t&&(a=(0,s.P)((0,s.collection)(n.db,o.COLLECTIONS.transactions),(0,s.My)(o.FIELD_NAMES.date,"desc"),(0,s.HM)(t),(0,s.AB)(e)));let r=await (0,s.getDocs)(a);return{transactions:r.docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data()[o.FIELD_NAMES.date])?void 0:t.toDate()}}),lastDoc:r.docs[r.docs.length-1]||null,hasMore:r.docs.length===e}}catch(e){throw console.error("Error getting transactions:",e),e}}async function h(){try{console.log("\uD83D\uDD0D Loading ALL pending withdrawals...");let e=(0,s.P)((0,s.collection)(n.db,o.COLLECTIONS.withdrawals),(0,s._M)("status","==","pending"),(0,s.My)("date","desc")),t=(await (0,s.getDocs)(e)).docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data().date)?void 0:t.toDate()}});return console.log("✅ Loaded ".concat(t.length," pending withdrawals")),t}catch(e){throw console.error("Error getting all pending withdrawals:",e),e}}async function g(){try{console.log("\uD83D\uDD0D Loading ALL withdrawals...");let e=(0,s.P)((0,s.collection)(n.db,o.COLLECTIONS.withdrawals),(0,s.My)("date","desc")),t=(await (0,s.getDocs)(e)).docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data().date)?void 0:t.toDate()}});return console.log("✅ Loaded ".concat(t.length," total withdrawals")),t}catch(e){throw console.error("Error getting all withdrawals:",e),e}}async function p(){try{console.log("\uD83D\uDD0D Loading ALL transactions...");let e=(0,s.P)((0,s.collection)(n.db,o.COLLECTIONS.transactions),(0,s.My)(o.FIELD_NAMES.date,"desc")),t=(await (0,s.getDocs)(e)).docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data()[o.FIELD_NAMES.date])?void 0:t.toDate()}});return console.log("✅ Loaded ".concat(t.length," total transactions")),t}catch(e){throw console.error("Error getting all transactions:",e),e}}async function y(e,t){try{await (0,s.mZ)((0,s.H9)(n.db,o.COLLECTIONS.users,e),t),r.delete("dashboard-stats")}catch(e){throw console.error("Error updating user:",e),e}}async function f(e){try{await (0,s.kd)((0,s.H9)(n.db,o.COLLECTIONS.users,e)),r.delete("dashboard-stats")}catch(e){throw console.error("Error deleting user:",e),e}}async function x(e,t,i){try{let l=await (0,s.x7)((0,s.H9)(n.db,o.COLLECTIONS.withdrawals,e));if(!l.exists())throw Error("Withdrawal not found");let{userId:c,amount:d,status:u}=l.data(),m={status:t,updatedAt:s.Dc.now()};if(i&&(m.adminNotes=i),await (0,s.mZ)((0,s.H9)(n.db,o.COLLECTIONS.withdrawals,e),m),"approved"===t&&"approved"!==u){let{addTransaction:e}=await Promise.resolve().then(a.bind(a,3592));await e(c,{type:"withdrawal_approved",amount:0,description:"Withdrawal approved - ₹".concat(d," processed for transfer")})}if("rejected"===t&&"rejected"!==u){let{updateWalletBalance:e,addTransaction:t}=await Promise.resolve().then(a.bind(a,3592));await e(c,d),await t(c,{type:"withdrawal_rejected",amount:d,description:"Withdrawal rejected - ₹".concat(d," credited back to wallet")})}r.delete("dashboard-stats")}catch(e){throw console.error("Error updating withdrawal status:",e),e}}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,8818,3592,6681,8441,1684,7358],()=>t(6463)),_N_E=e.O()}]);