(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8603],{545:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>o});var s=a(5155),i=a(2115),n=a(3004),c=a(5317),r=a(6104),l=a(3592);function o(){let[e,t]=(0,i.useState)(""),[a,o]=(0,i.useState)(!1),d=e=>{t(t=>t+e+"\n"),console.log(e)},m=async()=>{o(!0),t("");try{d("\uD83E\uDDEA Starting Firestore Rules Test...");let e="test".concat(Date.now(),"@example.com");d("\uD83D\uDCE7 Creating test user: ".concat(e));let t=(await (0,n.eJ)(r.j2,e,"test123456")).user;d("✅ Firebase Auth user created: ".concat(t.uid));let a={[l.FIELD_NAMES.name]:"Test User",[l.FIELD_NAMES.email]:e,[l.FIELD_NAMES.mobile]:"9876543210",[l.FIELD_NAMES.referralCode]:"TEST".concat(Date.now()),[l.FIELD_NAMES.referredBy]:"",[l.FIELD_NAMES.plan]:"Trial",[l.FIELD_NAMES.planExpiry]:null,[l.FIELD_NAMES.activeDays]:0,[l.FIELD_NAMES.joinedDate]:c.Dc.now(),[l.FIELD_NAMES.wallet]:0,[l.FIELD_NAMES.totalVideos]:0,[l.FIELD_NAMES.todayVideos]:0,[l.FIELD_NAMES.lastVideoDate]:null,[l.FIELD_NAMES.videoDuration]:30,status:"active"};d("\uD83D\uDCDD Creating Firestore document..."),d("User UID: ".concat(t.uid)),d("Auth state: ".concat(t.email," (verified: ").concat(t.emailVerified,")"));let s=(0,c.H9)(r.db,l.COLLECTIONS.users,t.uid);await (0,c.BN)(s,a),d("✅ Firestore document created successfully!");let i=await (0,c.x7)(s);i.exists()?(d("✅ Document verification successful!"),d("Document data: ".concat(JSON.stringify(i.data(),null,2)))):d("❌ Document verification failed!"),d("\uD83C\uDF89 All tests passed!")}catch(e){d("❌ Test failed: ".concat(e.message)),d("Error code: ".concat(e.code)),d("Full error: ".concat(JSON.stringify(e,null,2)))}finally{o(!1)}},u=async()=>{o(!0),t("");try{d("\uD83D\uDD10 Testing admin login...");let e=(await (0,n.x9)(r.j2,"<EMAIL>","123456")).user;d("✅ Admin logged in: ".concat(e.email)),d("Admin UID: ".concat(e.uid)),d("Email verified: ".concat(e.emailVerified));let t={test:!0,timestamp:c.Dc.now(),adminEmail:e.email},a=(0,c.H9)(r.db,"test_collection","admin_test");await (0,c.BN)(a,t),d("✅ Admin can create documents!")}catch(e){d("❌ Admin test failed: ".concat(e.message)),d("Error code: ".concat(e.code))}finally{o(!1)}};return(0,s.jsx)("div",{className:"min-h-screen p-4",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Firestore Rules Test"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[(0,s.jsx)("button",{onClick:m,disabled:a,className:"btn-primary",children:a?"Testing...":"Test User Registration"}),(0,s.jsx)("button",{onClick:u,disabled:a,className:"btn-secondary",children:a?"Testing...":"Test Admin Login"})]}),(0,s.jsxs)("div",{className:"glass-card p-4",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-white mb-4",children:"Test Output:"}),(0,s.jsx)("pre",{className:"text-white/80 text-sm whitespace-pre-wrap bg-black/20 p-4 rounded max-h-96 overflow-y-auto",children:e||"Click a test button to start..."})]}),(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsx)("a",{href:"/register",className:"btn-primary mr-4",children:"Go to Registration"}),(0,s.jsx)("a",{href:"/admin/login",className:"btn-secondary",children:"Go to Admin Login"})]})]})})}},6104:(e,t,a)=>{"use strict";a.d(t,{Cn:()=>m,db:()=>d,j2:()=>o});var s=a(3915),i=a(3004),n=a(5317),c=a(858),r=a(2144);let l=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),o=(0,i.xI)(l),d=(0,n.aU)(l);(0,c.c7)(l);let m=(0,r.Uz)(l,"us-central1")},6983:(e,t,a)=>{Promise.resolve().then(a.bind(a,545))}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8818,3592,8441,1684,7358],()=>t(6983)),_N_E=e.O()}]);